import time
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.chains.question_answering import load_qa_chain
from langchain_community.llms.llamacpp import LlamaCpp
from langchain_community.vectorstores import FAISS
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.document_loaders import CSVLoader
from datetime import datetime
import pandas as pd


loader = CSVLoader(file_path="current_cp_dataset.csv", encoding="utf-8", csv_args={'delimiter': ','})
pages = loader.load()


text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1024,
    chunk_overlap=64,
    separators=['\n\n', '\n', '(?=>\. )', ' ', '']
)
docs = text_splitter.split_documents(pages)

embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/average_word_embeddings_glove.6B.300d")


llm = LlamaCpp(
    model_path="openchat-3.5-0106.Q4_K_M.gguf",
    temperature=0.8,
    verbose=False,
    max_tokens=20,
    n_gpu_layers=1,
    stop=["Q:", "\n"],
    echo=True
)

chain = load_qa_chain(llm, chain_type="stuff")

db = FAISS.from_documents(docs, embeddings)


def gpt_three_point_five(query_input):

    docs = db.similarity_search(query_input)
    ans = chain.run(input_documents=docs, question=query_input + ", give me the intent only")
    return ans
    # print(f"response: {ans}")


if __name__ == "__main__":

    import pandas as pd
    import time
    from datetime import datetime


    def format_time(seconds):
        hours, remainder = divmod(seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{int(hours)} hours, {int(minutes)} minutes, {int(seconds)} seconds"


    if __name__ == "__main__":
        start_time = time.time()
        read_file = r"D:\opechat_local\questions.csv"
        df = pd.read_csv(read_file)

        text_response = []
        status_list = []
        p_s_time = []
        p_e_time = []
        p_exe_time = []

        for index, row in df.iterrows():
            text_data = row['text']

            predict_s_time = time.time()

            response_data = gpt_three_point_five(text_data)

            predict_e_time = time.time()

            predict_exe_time = predict_e_time - predict_s_time

            p_s_time.append(datetime.fromtimestamp(predict_s_time).strftime('%Y-%m-%d %H:%M:%S'))
            p_e_time.append(datetime.fromtimestamp(predict_e_time).strftime('%Y-%m-%d %H:%M:%S'))
            p_exe_time.append(predict_exe_time)

            print(f"Prediction Time : {predict_exe_time}")

            if response_data.startswith(" "):
                response_data = response_data[1:]
            response_data = response_data.replace('"', '')

            text_response.append(response_data)
            print(f"response: {response_data}")

            if row['intent'] == response_data:
                status_list.append('TRUE')
            else:
                status_list.append('FALSE')

        df["predicted"] = text_response
        df['status'] = status_list
        df['start_time'] = p_s_time
        df['end_time'] = p_e_time
        df['execution_time'] = p_exe_time

        end_time = time.time()
        execution_time = end_time - start_time

        # Calculate the counts of 'TRUE' and 'FALSE' values in the 'status' column
        status_counts = df['status'].value_counts()
        # Create a new column to store the counts of 'TRUE' and 'FALSE'
        df['status_counts'] = df['status'].map(status_counts)

        # Store the result of counts in another column without using lambda
        true_count = status_counts.get('TRUE', 0)
        false_count = status_counts.get('FALSE', 0)
        df['status_result'] = ['TRUE' if count == true_count else 'FALSE' for count in df['status_counts']]

        # Print the total counts of 'TRUE' and 'FALSE'
        print("Number of TRUE values:", true_count)
        print("Number of FALSE values:", false_count)

        print(f"Total execution time: {format_time(execution_time)}")

        excel_file_path = "sample4_sheets.xlsx"
        while True:
            try:
                with pd.ExcelWriter(excel_file_path) as writer:
                    df.iloc[:, :4].to_excel(writer, sheet_name='Sheet1', index=False)  # Sheet1 contains all columns

                    df.iloc[:, -5:].to_excel(writer, sheet_name='Sheet2', index=False)  # Sheet2 contains last 5 columns
                break
            except PermissionError:
                print(f"Please close the file '{excel_file_path}' and press Enter to continue...")
                input()

        df_excel_sheet1 = pd.read_excel(excel_file_path, sheet_name='Sheet1')  # Read only Sheet1
        df_excel_sheet1.to_csv("questions.csv", index=False)

        df_excel_sheet2 = pd.read_excel(excel_file_path, sheet_name='Sheet2')  # Read only Sheet2
        df_excel_sheet2.to_csv("questions.csv", index=False)

        print("Excel file converted to CSV successfully.")

        df.to_csv("questions.csv", index=False)
        print(df)
