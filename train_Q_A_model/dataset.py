import csv

# Define the input and output file paths
input_file = "data.txt"
output_file = "sachin_tendulkar_dataset.csv"

# Open the input and output files
with open(input_file, 'r') as f_input, open(output_file, 'w', newline='', encoding='utf-8') as f_output:
    # Create a CSV writer object
    csv_writer = csv.writer(f_output)
    # Write the header row
    csv_writer.writerow(['question', 'context', 'answer'])

    # Read the lines from the input file
    lines = f_input.readlines()
    # Process each line
    for line in lines:
        # Split the line into question, context, and answer
        parts = line.strip().split('","')
        if len(parts) == 3:
            question = parts[0].replace('"', '')
            context = parts[1]
            answer = parts[2].replace('"', '')
            # Write the data into the CSV file
            csv_writer.writerow([question, context, answer])
        else:
            print("Skipping invalid line:", line.strip())

print("Data has been written to:", output_file)
