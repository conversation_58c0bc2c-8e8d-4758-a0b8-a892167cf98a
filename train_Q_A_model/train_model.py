import pandas as pd

# Path to your CSV file
csv_file_path = r"D:\dhaya\PythonCodeCamp\train_Q_A_model\sachin_tendulkar_dataset.csv"

# Read the CSV file into a pandas DataFrame
df = pd.read_csv(csv_file_path)

# Add a new column named 'train' with empty strings
df['train'] = ''

# Save the modified DataFrame back to a CSV file
output_csv_file_path = "qa_dataset.csv"
df.to_csv(output_csv_file_path, index=False)

print("New column 'train' added and CSV file saved successfully.")
