Hey there! Ever wondered how computers can read and understand long documents like books? 
In this video, we will show you how they do it using something called a Large Language Model (L-L-M). 
We will break down the process step by step, from loading a PDF to answering questions based on what's inside. 
Get ready to see how technology can make learning and finding information easier than ever before!
First, import necessary libraries and modules for the code to function. 
It includes Torch for machine learning tasks, 
as well as various modules from the "langchain" package for document loading, text splitting, embeddings, vector stores, large language models, memory management, and conversational retrieval chains.
Next, Device Selection. we will verify if a CUDA-enabled GPU is accessible. 
<PERSON><PERSON> equals to torch dot device. "cuda" if torch dot, cuda dot, is_available. else, "cpu".
For machine learning tasks, CUDA is preferred as it offers faster processing, but if you don't have CUDA, you can still use the CPU. So, to adapt to different hardware setups, we check if a CUDA-enabled GPU is available. If it is, we assign it to the variable 'device', for faster computations. If not, we gracefully fall back to using the CPU.
Next, Document Loading. create a PDF document loader with a specified file path.
loader equals to Py-P-D-F-Loader. file_path equals to, p-d-f path.
Then, the load method is called to load the content of the P-D-F document.
data equals to, loader dot load.
Next, Text Splitting. initialize A text splitter object, with parameters defining the chunk size and overlap.
text_splitter equals to Recursive-Character-Text-Splitter(chunk_size equals to 10000, chunk_overlap equals to 200).
Then, The split_documents method is used to split the loaded document into chunks of text.
text_chunks equals to text_splitter dot split_documents, and pass, data.
Next, we are diving into the heart of our operation. the pivotal moment where we initialize the Large Language Model . Picture this: with just a few lines of code, we are summoning the power of language comprehension. Here it goes.
l-l-m-answer-gen equals to Lama-C-p-p.
streaming equals to True. This variable is set to True, indicating that the L-L-M will process data in a streaming manner, meaning it can handle large amounts of data by processing it in smaller, manageable chunks rather than all at once.
next, model_path equals to, the model path,
next, temperature equals to zero point seventy five. This parameter controls the randomness of the L-L-M's output. A higher temperature results in more diverse and creative responses, while a lower temperature produces more deterministic and conservative responses.
next, top_pee equals to one. This variable, often referred to as nucleus sampling or top-pee sampling, determines the cumulative probability cut-off for generating the model's response. A value of one means the model considers all possibilities, while smaller values restrict the selection to a subset of the most likely tokens.
next, f-sixteen_k_v equals to True. This variable is set to True, indicating that the L-L-M uses sixteen-bit floating-point precision for its key-value pairs. This can help reduce memory usage and speed up computations, especially on G-P-U with limited memory capacity.
next, verbose equals to False. When set to False, this variable suppresses additional output or diagnostic information during the LLM's operation, keeping the process clean and concise. 
and finally, n_c-t-x equals to four zero nine six. This variable determines the maximum length of the input context (in tokens) that the LLM can consider when generating responses. In this case, it's set to four zero nine six, allowing the model to consider a substantial amount of context for generating responses.
Thats it, This block is where the magic begins. We are essentially initializing  L-L-M as a digital brain to understand and respond to questions based on the input data. Its like flipping a switch and bringing a sophisticated language processing system to life, all set to unravel the mysteries hidden within our documents.
Next, Embeddings Initialization.
embeddings equals to Hugging-Face-Embeddings. model_name equals to, the model name, and  model_kwargs equals to, a dictionary with the key 'device' mapped to the device we initialized at the beginning.
Next,Vector Store Initialization.
vector_store equals to Chroma dot from_documents. (text_chunks and embeddings).
This vector_store will allow us to efficiently retrieve information and perform operations based on the semantic similarity of the text
Next, lets initialize a  memory object with specified memory key and return messages settings.
memory equals to Conversation-Buffer-Memory. memory_key equals to "chat_history", and  return_messages equals to True.
This memory is to store chat history and retrieve messages when needed, providing a crucial mechanism for maintaining context and continuity in conversations.
Next, initialize A conversational retrieval chain using the L-L-M, vector store retriever, and memory.
answer_gen_chain equals to Conversational-Retrieval-Chaindot from_l-l-m. l-l-m equals to l-l-m answer_gen, retriever equals to vector_store dot as_retriever, memory equals to memory.
This enables the system to generate answers to user queries in a conversational context.
All set. Now, let's dive into the exciting part – create a loop that lets us engage with our newly minted conversational system.
while True:
user_input equals to input. Enter a question.
if user_input dot lower is exit, break.
answer equals to answer_gen_chain dot run, pass the user input in a dictioary, with the key "question".
and finally print the answer.
Lets run the code.



 





