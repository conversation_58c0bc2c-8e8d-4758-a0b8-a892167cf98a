import os
import gradio as gr
from dotenv import load_dotenv
from PyPDF2 import PdfReader
from langchain.text_splitter import CharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS
from langchain.chat_models import ChatOpenAI
from langchain.memory import Conversation<PERSON>ufferMemory
from langchain.chains import ConversationalR<PERSON>rie<PERSON><PERSON>hain


def get_pdf_text(pdf_files):
    text = ""
    for pdf in pdf_files:
        pdf_reader = PdfReader(pdf)
        for page in pdf_reader.pages:
            text += page.extract_text()
    return text


def get_text_chunks(text):
    text_splitter = CharacterTextSplitter(
        separator="\n",
        chunk_size=1000,
        chunk_overlap=200,
        length_function=len
    )
    chunks = text_splitter.split_text(text)
    return chunks


def get_vectorstore(text_chunks):
    embeddings = OpenAIEmbeddings()
    vectorstore = FAISS.from_texts(texts=text_chunks, embedding=embeddings)
    return vectorstore


def get_conversation_chain(vectorstore):
    llm = ChatOpenAI()
    memory = ConversationBufferMemory(
        memory_key='chat_history', return_messages=True)
    conversation_chain = ConversationalRetrievalChain.from_llm(
        llm=llm,
        retriever=vectorstore.as_retriever(),
        memory=memory
    )
    return conversation_chain


class ChatBot:
    def __init__(self):
        self.conversation = None
        self.chat_history = []

    def process_files(self, pdf_files):
        if not pdf_files:
            return "Please upload PDF files first."

        # get pdf text
        raw_text = get_pdf_text(pdf_files)

        # get the text chunks
        text_chunks = get_text_chunks(raw_text)

        # create vector store
        vectorstore = get_vectorstore(text_chunks)

        # create conversation chain
        self.conversation = get_conversation_chain(vectorstore)

        return "PDFs processed successfully! You can now start chatting."

    def chat(self, message, history):
        if self.conversation is None:
            return "Please process PDF files first before asking questions."

        response = self.conversation({'question': message})
        self.chat_history = response['chat_history']

        # Return only the bot's response
        return response['chat_history'][-1].content


def main():
    load_dotenv()

    chatbot = ChatBot()

    with gr.Blocks(title="Chat with multiple PDFs") as demo:
        gr.Markdown("# Chat with multiple PDFs 📚")

        with gr.Row():
            with gr.Column(scale=1):
                file_output = gr.File(
                    file_count="multiple",
                    label="Upload your PDFs here"
                )
                process_button = gr.Button("Process PDFs")

            with gr.Column(scale=2):
                chatbot_interface = gr.Chatbot(
                    label="Chat History",
                    height=400
                )
                msg = gr.Textbox(
                    label="Ask a question about your documents",
                    placeholder="Type your question here...",
                    lines=2
                )

        process_button.click(
            fn=chatbot.process_files,
            inputs=[file_output],
            outputs=[gr.Textbox(label="Status")]
        )

        msg.submit(
            fn=chatbot.chat,
            inputs=[msg, chatbot_interface],
            outputs=chatbot_interface
        )

    demo.launch()


if __name__ == "__main__":
    main()