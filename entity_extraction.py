sentence = ("For the Business Service XYZ_LOGISTICS_SYSTEM, "
            "For the Business Function BF_SUPPLY_CHAIN_MANAGEMENT i need the server list ")

ner = []
words = sentence.split()
base_business_list = ["business", "business."]
base_infra_list = ["infra", "infra."]

function_next_list = ["function", "function."]
service_next_list = ["service", "service."]
object_next_list = ["object", "object.", "objects", "objects."]
component_next_list = ["component", "component.", "components", "components."]


skip_next_two = False
business_index = []
for idx, word in enumerate(words):
    if business_index:
        if idx < business_index[0]:
            continue
        elif idx == business_index[0]:
            business_index.clear()
            continue

    if word.lower() in base_business_list:
        base_string = "B-"
        inside_string = ""
        if idx + 1 <= len(words):
            next_word = words[idx + 1].lower()
            if next_word in function_next_list:
                inside_string += "BF"
                base_string += inside_string
                ner.append(base_string)
            elif next_word in service_next_list:
                inside_string += "BS"
                base_string += inside_string
                ner.append(base_string)

        if idx + 3 <= len(words):
            if inside_string:
                ner.extend([f"I-{inside_string}"] * 2)  # Appending "I-BF" for the next two words
                business_index.append(idx+2)

    elif word.lower() in base_infra_list:
        base_string = "B-"
        inside_string = ""
        if idx + 1 <= len(words):
            next_word = words[idx + 1].lower()
            if next_word in object_next_list:
                inside_string += "IO"
                base_string += inside_string
                ner.append(base_string)
            elif next_word in component_next_list:
                inside_string += "IC"
                base_string += inside_string
                ner.append(base_string)

        if idx + 3 <= len(words):
            if inside_string:
                ner.extend([f"I-{inside_string}"] * 2)  # Appending "I-BF" for the next two words
                business_index.append(idx+2)
    else:
        ner.append("O")

print(words)
print(ner)
