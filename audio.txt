this pipeline requires three arguments the first argument specifies the type of pipeline to create in this case a question answering pipeline the second argument specifies the model to be used in the NLP pipeline with the variable model assigned to the model parameter of the pipeline function the third argument specifies the tokenizer to be used in the NLP pipeline the pipeline expect to receive a dictionary with the question in the context what is full name