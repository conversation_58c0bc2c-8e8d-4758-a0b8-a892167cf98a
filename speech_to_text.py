from transformers import WhisperProcessor, WhisperForConditionalGeneration
import soundfile as sf
from scipy.signal import resample

# load model and processor
processor = WhisperProcessor.from_pretrained("openai/whisper-small")
model = WhisperForConditionalGeneration.from_pretrained("openai/whisper-small")
model.config.forced_decoder_ids = None

# Replace 'your_audio_file.wav' with the path to your own audio file
audio_file_path = 'test_audio.wav'

# Read your audio file
audio_data, original_sampling_rate = sf.read(audio_file_path)

# Resample audio data to 16000 Hz
target_sampling_rate = 16000
audio_data_resampled = resample(audio_data, int(len(audio_data) * target_sampling_rate / original_sampling_rate))

# Process resampled audio data
input_features = processor(audio_data_resampled, sampling_rate=target_sampling_rate, return_tensors="pt").input_features

# Generate token ids
predicted_ids = model.generate(input_features)
# Decode token ids to text
transcription = processor.batch_decode(predicted_ids, skip_special_tokens=False)

print(transcription)




































