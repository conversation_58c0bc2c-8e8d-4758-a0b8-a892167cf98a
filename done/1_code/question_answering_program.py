from transformers import AutoModelForQuestionAnswering, AutoTokenizer, pipeline

model_name = "deepset/roberta-base-squad2"

# Load model & tokenizer
model = AutoModelForQuestionAnswering.from_pretrained(model_name)
tokenizer = AutoTokenizer.from_pretrained(model_name)

# Create a question-answering pipeline
nlp = pipeline('question-answering', model=model, tokenizer=tokenizer)

context = "your context"

while True:
    user_input = input("Ask a question (or press 'q' to quit): ")

    if user_input.lower() == 'q':
        break

    QA_input = {
        'question': user_input,
        'context': context }

    res = nlp(QA_input)
    print(f"Answer: {res['answer']}")
