# import nltk
# def has_underscore(input_string):
#     return '_' in input_string
#
# symbols = [",", "'", "."]
#
# def check_non_english_word(sentence):
#     non_english_tokens = []  # Renamed from non_english_words to avoid conflict
#     non_englis_words = {}
#
#     try:
#         # Initialize the English dictionary
#         english_words = set(nltk.corpus.words.words())
#
#         # Add specific technical words
#         technical_words = {"hostname", "unmount"}
#         non_english_words_set = {"pr", "dr"}
#
#         # Tokenize the sentence
#         word_tokens = nltk.word_tokenize(sentence)
#
#         # Check each word
#         for idx, word in enumerate(word_tokens):
#             # Check if the word is in the English dictionary
#             if word.lower() in english_words:
#                 # Check if it's a technical word or a short word (considered non-English)
#                 if word.lower() in technical_words or len(word) == 1 or word.lower() in non_english_words_set:
#                     non_english_tokens.append(word)
#                     # print(f"{word} is not an English word")
#                     if not ((len(word) < 4) and (word in symbols)):
#                         non_englis_words[idx] = word
#                 elif has_underscore(word):
#                     # print(f"{word} is not an English word")
#                     if not ((len(word) < 3) or (word in symbols)):
#                         non_englis_words[idx] = word
#                 # else:
#                 #     print(f"{word} is an English word")
#             else:
#                 # print(f"{word} is not an English word")
#                 if not ((len(word) < 4) or (word in symbols)):
#                     non_englis_words[idx] = word
#
#         # Check if non_english_tokens list has at least one item
#         non_english = None
#         if non_english_tokens:
#             non_english = " ".join(non_english_tokens)
#
#         return non_englis_words
#     except Exception as ex:
#         print(ex)
#
# sentence = ("show me the drill report of the business function bf234 ")
# e_names = check_non_english_word(sentence)
#
#
# ner = []
# words = sentence.split()
# base_business_list = ["business", "business."]
# base_infra_list = ["infra", "infra."]
#
# function_next_list = ["function", "function."]
# service_next_list = ["service", "service."]
# object_next_list = ["object", "object.", "objects", "objects."]
# component_next_list = ["component", "component.", "components", "components."]
#
# skip_next_two = False
# business_index = []
#
# entity = {}
# values = []
#
# for idx, word in enumerate(words):
#     if business_index:
#         if idx < business_index[0]:
#             continue
#         elif idx == business_index[0]:
#             business_index.clear()
#             continue
#
#     if word.lower() in base_business_list:
#         if idx + 1 <= len(words):
#             next_word = words[idx + 1].lower()
#             if next_word in function_next_list:
#                 entity[idx] = "Business Function"
#             elif next_word in service_next_list:
#                 entity[idx] = "Business Service"
#
#     elif word.lower() in base_infra_list:
#         if idx + 1 <= len(words):
#             next_word = words[idx + 1].lower()
#             if next_word in object_next_list:
#                 entity[idx] = "Infra Object"
#             elif next_word in component_next_list:
#                 entity[idx] = "Infra Component"
#
#
# print(entity)
# print(e_names)
#
# # dict1 = entity
# # dict2 = e_names
#
# # Find the smaller and larger dictionaries
# smaller_dict, larger_dict = (entity, e_names) if len(entity) < len(e_names) else (e_names, entity)
#
# # Fill the smaller dictionary with dummy values
# dummy_key = "dummy"
# dummy_value = "dummy_value"
#
# while len(smaller_dict) < len(larger_dict):
#     smaller_dict[100] = dummy_value
#
# dict1 = entity
# dict2 = e_names
# keys1 = list(dict1.keys())
#
# for i, (key2, value2) in enumerate(dict2.items()):
#     for j, (key1, value1) in enumerate(dict1.items()):
#         if i + 1 < len(keys1):
#             if isinstance(key2, int):
#                 # Use isinstance for type checking
#                 if keys1[j] < key2:
#                     if j + 1 < len(keys1):
#                         if key2 < keys1[j + 1]:
#                             print(f"{dict1[keys1[j]]} : {value2}")
#                             break
#                     else:
#                         print(f"{dict1[keys1[j]]} : {value2}")
#                         break
#         elif i == len(keys1) - 1:
#             print(f"{dict1[keys1[len(keys1)-1]]} : {value2}")
#             break
#
# # keys1 = list(dict1.keys())
# # # print(keys1)
# #
# # for i, (key1, value1) in enumerate(dict2.items()):
# #     # print(i)
# #     # print(key1, value1)
# #     if i + 1 < len(keys1):
# #         if type(key1) is int:
# #             if (key1 > keys1[i]) and (key1 < keys1[i+1]):
# #                 print(f"{dict1[keys1[i]]} : {value1}")
# #     elif i == len(keys1) - 1:
# #         if type(key1) is int:
# #             if key1 > keys1[i]:
# #                 print(f"{dict1[keys1[i]]} : {value1}")
#
#     # for key2, value2 in dict2.items():
#     #     if i != len(keys1) - 1:
#     #         if i + 1 < len(keys1):
#     #             if key2 < keys1[i + 1]:
#     #                 print(f"{value1} : {value2}")
#     #     else:
#     #         if key2 < keys1[i + 1]:
#     #             print(f"{value1} : {value2}")
#
#
#
# # dict1 = {2: 'Business Function', 12: 'Infra Component', 16: 'Business Function'}
# # dict2 = {15: 'av3ar', 22: 'baymax31', "dummy" : "dummy_value"}










employees = {
    "E101": {
        "name": "Rahul Kumar",
        "age": 28,
        "department": "Development",
        "skills": ["Python", "Django", "SQL"],
        "projects": [
            {"name": "Website Revamp", "status": "Ongoing"},
            {"name": "Internal Tool", "status": "Completed"}
        ]
    },
    "E102": {
        "name": "Anjali Mehta",
        "age": 25,
        "department": "Data Science",
        "skills": ["Python", "Pandas", "ML"],
        "projects": [
            {"name": "Sales Prediction", "status": "Ongoing"},
            {"name": "Customer Segmentation", "status": "Ongoing"}
        ]
    },
    "E103": {
        "name": "Vikram Rao",
        "age": 30,
        "department": "DevOps",
        "skills": ["AWS", "Docker", "Linux"],
        "projects": [
            {"name": "Server Migration", "status": "Completed"},
            {"name": "Monitoring Setup", "status": "Ongoing"}
        ]
    }
}
