[{"os": [{"categoryName": "Operating System", "workflowCategoryBaseChildViewListVms": [{"name": "COMMON", "workflowCategoryChildViewListVms": [{"name": "Common", "actionLists": [{"actionName": "Execute_Check_Power_Shell_Command"}, {"actionName": "Verify_Installed_SSH"}, {"actionName": "UI_Automation_With_Password"}, {"actionName": "UI_Automation"}, {"actionName": "UAC_Disable"}, {"actionName": "Test_SRM_Connectivity"}, {"actionName": "Stop_MS_Cluster"}, {"actionName": "Start_Scheduled_Task_Job"}, {"actionName": "Start_MS_Cluster"}, {"actionName": "Kill_Unix_Process"}, {"actionName": "Is_Cluster_Online"}, {"actionName": "Is_Cluster_Offline"}, {"actionName": "Install_Open_SSH_New"}, {"actionName": "Install_Open_SSH"}, {"actionName": "Folder_Compare"}, {"actionName": "File_Compare"}, {"actionName": "Execute_Script_With_User_Password"}, {"actionName": "Execute_Script_With_Password"}, {"actionName": "Execute_Script_With_En_v_Password"}, {"actionName": "Execute_Script_Along_Password"}, {"actionName": "Execute_Script"}, {"actionName": "Execute_SSH_Single_Session"}, {"actionName": "Execute_OSZFS_Command"}, {"actionName": "Execute_OS_Command_With_Password"}, {"actionName": "Execute_Local_Process"}, {"actionName": "Execute_Check_OS_Command_With_Password"}, {"actionName": "Compare_File_OR_Folder"}, {"actionName": "Cluster_Remove_Dependency"}, {"actionName": "Cluster_Add_Dependency"}, {"actionName": "Change_Node_IP"}, {"actionName": "Application_Service_Monitor"}, {"actionName": "Add_User_Administrator_Group"}, {"actionName": "Execute_Power_Shell_Command"}]}]}, {"name": "Windows", "workflowCategoryChildViewListVms": [{"name": "DFSServerAutomation", "actionLists": [{"actionName": "Enable_Folder_Target_Referral_Names_pace"}, {"actionName": "Disable_Folder_Target_Referral_Names_pace"}, {"actionName": "Check_Folder_Target_Referral_Names_pace_Status"}]}, {"name": "Common", "actionLists": [{"actionName": "Wait"}, {"actionName": "wink_ill_Process_By_Name"}, {"actionName": "win_Is_File_Exist"}, {"actionName": "Win_Rename_File"}, {"actionName": "Win_Change_File_Text"}, {"actionName": "Wait_For_Ping"}, {"actionName": "Stop_Scheduled_Task"}, {"actionName": "Start_Scheduled_Task"}, {"actionName": "Shutdown_Remote_Server"}, {"actionName": "Shut_Down_Server"}, {"actionName": "SC_P"}, {"actionName": "Replicate_Stand_By_Ctr_l_File"}, {"actionName": "Replicate_Data_Sync_Folders_Win"}, {"actionName": "Replicate_Data_Sync_File_Win"}, {"actionName": "Replace_Txt_In_File"}, {"actionName": "Replace_File"}, {"actionName": "Rm_v_NT_AUTHORITY_Systm_Ac_n_t_Fm_DNS_Rcrd_With_Al_w_Or_D_n_y"}, {"actionName": "Rm_v_e_Cmptr_Acunt_Frm_DNS_Rcd_Wth_Al_o_Or_D_n_y_Pr_ms_son"}, {"actionName": "Reboot_Remote_Server"}, {"actionName": "R_Replication"}, {"actionName": "Modify_DNS_Record_Value_A_Type_without_TTL"}, {"actionName": "Modify_DNS_Record_Value_A_Type_with_TTL"}, {"actionName": "MSS_QL_Server_Installed_SSMS_Install"}, {"actionName": "MSS_QL_Server_Installed"}, {"actionName": "Execute_Windows_Process"}, {"actionName": "Execute_Symcli_Command"}, {"actionName": "Execute_SSH"}, {"actionName": "Execute_OS_Cmd"}, {"actionName": "Execute_Check_Symcli_Command"}, {"actionName": "Execute_Batch_File"}, {"actionName": "Enable_Scheduled_Task"}, {"actionName": "Disable_Scheduled_Task"}, {"actionName": "Delete_DNS"}, {"actionName": "Data_Sync_Replicate_File"}, {"actionName": "Data_Sync_Redo_Archive_Folder"}, {"actionName": "Data_Sync_Folders"}, {"actionName": "Data_Sync_Archive_Folder"}, {"actionName": "DNS_Server_Set_TTL_Value_for_DNS_SOA"}, {"actionName": "Check_String_Value_Exist_In_File"}, {"actionName": "Check_Service_Status"}, {"actionName": "Check_Scheduled_Task_Status"}, {"actionName": "Check_File_Exist"}, {"actionName": "Check_DNS_Record_without_TTL_Value_A_Type"}, {"actionName": "Check_DNS_Record_with_TTL_value_A_Type"}, {"actionName": "Change_Service_Start_Mode"}, {"actionName": "Change_DNS"}, {"actionName": "Batch_File_Execution"}, {"actionName": "Application_Stop"}, {"actionName": "Application_Start"}, {"actionName": "Add_new_record_Txt_File"}, {"actionName": "Add_NT_AUTHORITY_Systm_Acunt_To_DNS_Rcrd_Wth_Deny_Prmsn"}, {"actionName": "Add_NT_AUTHORITY_Systm_Acunt_T_DNS_Rcrd_Wth_Alow_Prmsn"}, {"actionName": "Add_DNS"}, {"actionName": "Add_Cmptr_Account_To_DNS_Recod_Wth_Deny_Permission"}, {"actionName": "Add_Cmptr_Account_To_DNS_Recd_Wth_Allow_Prmsion"}, {"actionName": "Win_Is_Service_Running"}, {"actionName": "Win_Is_Service_Down"}, {"actionName": "Stop_Windows_Service"}, {"actionName": "Stop_Service"}, {"actionName": "Start_Service"}, {"actionName": "Start_Windows_Service"}]}, {"name": "CitrixNetScalar", "actionLists": [{"actionName": "Enable_Nets_cal_er_Load_Balancing_Server"}, {"actionName": "Disable_Net_Scaler_Load_Balancing"}, {"actionName": "Check_Net_Scaler_Load_Balancer_Status"}]}, {"name": "ActiveDirectory", "actionLists": [{"actionName": "Verify_DNS_C_NAME_Record"}, {"actionName": "Transfer_Or_Seizure_FSMO_Role"}, {"actionName": "Remove_SPN_From_Active_Directory_Computer"}, {"actionName": "Modify_C_NAME_with_TTL"}, {"actionName": "Modify_Are_cord_with_TTL"}, {"actionName": "DC_Server"}, {"actionName": "DNS_Verify"}, {"actionName": "DNS_Modify"}, {"actionName": "Check_Active_Directory_Replication_Sync_All_Status"}, {"actionName": "Check_Active_Directory_Replication_Status"}, {"actionName": "Check_AD_Forest_FSMO_Role"}, {"actionName": "Check_ADD_om_ain_FSMO_Role"}, {"actionName": "Change_DNS_Glue_Record"}, {"actionName": "Add_SPN_To_Active_Directory_Computer"}, {"actionName": "Active_Directory_Add_User"}, {"actionName": "ADC_heck_Replication_Sync_All_Status"}, {"actionName": "AD_Replication_Status"}]}, {"name": "Win2k8", "actionLists": [{"actionName": "Win2k8_Verify_Cluster_Resource_State"}, {"actionName": "Win2k8_Stop_Cluster_Resource"}, {"actionName": "Win2k8_Start_Cluster_Resource"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "actionLists": [{"actionName": "On_Line"}, {"actionName": "Off_Line"}, {"actionName": "Import"}, {"actionName": "Disk_Rescan"}, {"actionName": "Disk_Online_with_Drive_Letter"}, {"actionName": "Check_Disk_Status"}]}, {"name": "PowerShellDiskPart", "actionLists": [{"actionName": "Make_Disk_Read_Write"}, {"actionName": "Make_Disk_Online"}, {"actionName": "Make_Disk_Offline"}, {"actionName": "Check_Drive_Letter_after_Disk_Online"}, {"actionName": "Check_Disk_Details_Using_LUN_WWN_Number"}, {"actionName": "Change_Disk_Drive_Letter"}]}]}, {"name": "MainFrame", "workflowCategoryChildViewListVms": [{"name": "AS400", "actionLists": [{"actionName": "Execute_5250_Transaction_Count_Check_Popup"}, {"actionName": "Execute_5250_Process"}, {"actionName": "AS_400_Execute_Monitor"}]}]}, {"name": "IBM_AIX", "workflowCategoryChildViewListVms": [{"name": "VIOS", "actionLists": [{"actionName": "Check_Client_LP_AR_Status"}, {"actionName": "Check_Client_Boot_Disk_Mapping"}, {"actionName": "Activate_Client_LP_AR"}, {"actionName": "Wait_For_LP_AR_State"}, {"actionName": "Shut_Down_Client_LP_AR"}, {"actionName": "Check_Ethernet_Status"}]}, {"name": "Common", "actionLists": [{"actionName": "Vary_On_Volume_Groups"}, {"actionName": "Vary_Off_Volume_Groups"}, {"actionName": "Unmount_Volume_Groups"}, {"actionName": "Unmount_VGS"}, {"actionName": "Unmount_VG"}, {"actionName": "Unmount_NFS_Volume"}, {"actionName": "Replicate_Stand_By_Trace_File"}, {"actionName": "Replicate_Data_Sync_Folders_Posix"}, {"actionName": "Replicate_Data_Sync_Folders_DR"}, {"actionName": "Replicate_Data_Sync_File_Posix"}, {"actionName": "Remove_HDisk"}, {"actionName": "RM_DE_VS_Volume_Groups"}, {"actionName": "Mount_Volume_Groups"}, {"actionName": "Mount_VGS"}, {"actionName": "Mount_VG"}, {"actionName": "Mount_NFS_Volume"}, {"actionName": "Kill_Process"}, {"actionName": "HP_Unmount_VGS"}, {"actionName": "HP_Unmount_VG_Parallel"}, {"actionName": "HPUX_VG_Change_Deactive"}, {"actionName": "HPUX_VG_Change_Active"}, {"actionName": "HPUX_Unmount"}, {"actionName": "HPUX_Mount"}, {"actionName": "HPUX_Is_VGA_ct_iv_e"}, {"actionName": "HPUX_Import_VG_To_Map_File"}, {"actionName": "HPUX_Export_VG_To_Map_File"}, {"actionName": "HP_Mount_VGS"}, {"actionName": "HP_Mount_VG_Parallel"}, {"actionName": "Export_Volume_Groups"}, {"actionName": "Execute_No_h_up"}, {"actionName": "Execute_CPS_L"}, {"actionName": "Enable_SLB_Real_Services"}, {"actionName": "Disable_SLB_Real_Services"}, {"actionName": "Check_SLB_Real_Service_Name_Status"}, {"actionName": "AIX_Mount"}, {"actionName": "AIX_DisMount"}, {"actionName": "AIX_Change_File_Text"}, {"actionName": "Execute_CPL"}, {"actionName": "Execute_OS_Command"}, {"actionName": "Execute_Check_OS_Command"}]}]}, {"name": "HPUX", "workflowCategoryChildViewListVms": []}, {"name": "Generic_os_Action", "workflowCategoryChildViewListVms": []}, {"name": "OracleRsync", "workflowCategoryChildViewListVms": [{"name": "OracleRsync", "actionLists": [{"actionName": "Replicate_RS_y_n_c_Folders_Posix"}, {"actionName": "Replicate_RS_y_n_c_File_Posix"}]}, {"name": "OracleRsync", "actionLists": [{"actionName": "Ora_Rs_y_n_c_Replicate_Stand_By_Control_File"}, {"actionName": "Ora_Rs_y_n_c_Replicate_Stand_By_Trace_File"}]}]}, {"name": "AS400", "workflowCategoryChildViewListVms": [{"name": "Execute5250", "actionLists": []}]}, {"name": "AIX  LINUX UNIX", "workflowCategoryChildViewListVms": [{"name": "AIX LINUX UNIX", "actionLists": [{"actionName": "Execute_Check_XCLI_Command"}]}]}]}, {"categoryName": "Hypervisor", "workflowCategoryBaseChildViewListVms": [{"name": "Hypervisor", "workflowCategoryChildViewListVms": [{"name": "HyperVCluster", "actionLists": [{"actionName": "Verify_status_of_VM"}, {"actionName": "Verify_Failed_Over_Waiting_Completion_Status"}, {"actionName": "Verify_State"}, {"actionName": "Verify_<PERSON><PERSON>_Repli_Connection_config"}, {"actionName": "Verify_Replication_Mode"}, {"actionName": "Verify_Replication_State"}, {"actionName": "Verify_Prepared_For_Fail_overs_tat_us"}, {"actionName": "Verify_Hyper_VVM_V_LAN_ID_Connected_Status"}, {"actionName": "Verify_Hyper_VVM_DisConnected_Status"}, {"actionName": "Verify_Hyper_VVM_Connected_Status"}, {"actionName": "Verify_Forward_Repli_Connection_config"}, {"actionName": "Start_VM"}, {"actionName": "Start_Fail_over"}, {"actionName": "Single_Net_Clster_Hypr_VVM_IPAddress_Wth_Mac_Address"}, {"actionName": "Shutdown_VM"}, {"actionName": "Set_V_LAN_ID_To_Virtual_Switch_On_Hyper_VVM"}, {"actionName": "Hyper_V_Resume_Replication"}, {"actionName": "Fails_Over_Replica_VM"}, {"actionName": "Disconnection_To_Hyper_VVM"}, {"actionName": "Connection_To_Virtual_Switch_On_Hyper_VVM"}, {"actionName": "Cluster_Verify_Primary_Replication_Mode"}, {"actionName": "Change_Replication_mode"}, {"actionName": "Change_DNS_IP"}, {"actionName": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Hypr_VVM_IPAddress_Wth_Out_Mac_Address"}, {"actionName": "Change_Cluster_Hyper_VVM_IPAddress_With_Mac_Address"}, {"actionName": "Cancel_Fail_Over_Cluster_Hyper_VVM"}, {"actionName": "Single_Net_Clster_Hypr_VVM_IPAddress_Wth_uy_Mac_Adres"}, {"actionName": "Hyper_VM_on_it_or_in_g_Action"}]}, {"name": "MSSQLCluster", "actionLists": [{"actionName": "Rmove_Clstr_Resorce_Dpndncy_Frm_Dpndnt_Resorce"}, {"actionName": "Add_Cluster_Resource_Dependency_To_D_p_n_dent_Resorce"}, {"actionName": "Verify_Cluster_Resource_Dpndncy_In_Dependent_Rs_orc_e"}, {"actionName": "Remove_Cluster_Resource_From_Cluster_Owner_Group"}, {"actionName": "Add_Cluster_Resource_To_Cluster_Owner_Group"}]}]}, {"name": "Microsoft", "workflowCategoryChildViewListVms": [{"name": "HyperV", "actionLists": [{"actionName": "Verify_Disk_Status_On_Cluster_Shared_Volume"}, {"actionName": "Verify_Clstr_Shared_Volume_Disk_in_Fail_Over_Clstr"}, {"actionName": "Remove_Virtual_Sharing_Hard_Disk_Duplicate_VM"}, {"actionName": "Remove_Virtual_Sharing_Hard_Disk"}, {"actionName": "Enable_Virtual_Hard_Disk_Sharing_Optn_Duplicate_VM"}, {"actionName": "Enable_Virtual_Hard_Disk_Sharing_Option"}, {"actionName": "Disable_Virtual_Hard_Disk_Sharing_Optn_Duplcte_VM"}, {"actionName": "Disable_Virtual_Hard_Disk_Sharing_Option"}, {"actionName": "Chk_Virtual_HardDsk_Sharing_Optn_Status_Duplcte_VM"}, {"actionName": "Check_Virtual_Hard_Disk_Sharing_Option_Status"}, {"actionName": "Add_New_Virtual_Hard_Disk_Duplicate_VM"}, {"actionName": "Add_New_Virtual_Hard_Disk"}, {"actionName": "Hyper_VM_on_it_or_in_g"}, {"actionName": "Verify_Power_Off_Status_Of_Replica_PR"}, {"actionName": "Verify_Running_Status_Of_Primary_DR"}, {"actionName": "Start_Primary_Hyper_VVM_DR"}, {"actionName": "Verify_Replica_Replication_Mode_PR"}, {"actionName": "Verify_Hyper_V_Replication_State_PR"}, {"actionName": "Verify_Primary_Replication_Mode"}, {"actionName": "Verify_Hyper_V_Replication_State"}, {"actionName": "Rep_Mode_To_Primary_Rep_And_Start_Rep"}, {"actionName": "Verify_Fail_Over_Wait_Com_Status_Rep"}, {"actionName": "Fails_Over_Replica_On_Target_Host_DR"}, {"actionName": "Verify_Prepared_Fail_over_Status_PR"}, {"actionName": "Start_Fail_over_Primary_Rep_Pending"}, {"actionName": "Shutdown_Primary_Hyper_VO_ff"}, {"actionName": "Shutdown_Primary_Hyper_V"}, {"actionName": "Verify_Reverse_Rep_Connection_Configuration_DR"}, {"actionName": "Verify_Forward_Rep_Connection_Configuration_PR"}, {"actionName": "Verify_Primary_Replication_Mode_PR"}, {"actionName": "Verify_Replica_Replication_Mode_DR"}, {"actionName": "Verify_Hyper_V_Replication_State_DR"}]}, {"name": "Hyper V", "actionLists": [{"actionName": "Virtual_Switch_Verify_Exist_Status"}, {"actionName": "Verify_Reverse_Replication_Connection"}, {"actionName": "Verify_Forward_Replication_Connection"}, {"actionName": "Verify_Failed_Over_Waiting_Completion_Status"}, {"actionName": "VM_Network_Adapter_Verify_Status"}, {"actionName": "VM_Network_Adapter_Verify_V_LAN_IDS_tat_us"}, {"actionName": "Stop_VM"}, {"actionName": "Start_VM_Fail_Over"}, {"actionName": "Single_Net_Hyper_VVM_IPAddress_With_Out_Mac_Address"}, {"actionName": "Single_Net_Hyper_VVM_IPAddress_With_Mac_Address"}, {"actionName": "Set_VM_Reverse_Replication"}, {"actionName": "Set_V_LAN_ID_To_Virtual_Switch"}, {"actionName": "Set_Network_Adapter_Connection"}, {"actionName": "Prepare_VM_Fail_Over"}, {"actionName": "Execute_Check_Hyper_VC_om_man_d"}, {"actionName": "Disconnect_Network_Adapter_Connection"}, {"actionName": "Check_VM_State"}, {"actionName": "Check_VM_Replication_State"}, {"actionName": "Check_VM_Replication_Mode"}, {"actionName": "Check_Hyper_V_Replication"}, {"actionName": "Change_Hyper_VVM_IPAddress_With_Out_Mac_Address"}, {"actionName": "Change_Hyper_VVM_IPAddress_With_Mac_Address"}, {"actionName": "Cancel_Fail_Over_Standalone_Hyper_VVM"}]}]}]}, {"categoryName": "VMWare", "workflowCategoryBaseChildViewListVms": [{"name": "Nutanix", "workflowCategoryChildViewListVms": [{"name": "Nutanix", "actionLists": [{"actionName": "Verify_Target_Local_Av_lab_l_t_y_Zone_for_Leap_R_c_v_r_y_Pl_n"}, {"actionName": "Verify_Source_Failed_Av_lb_l_t_y_Zone_for_Leap_R_c_v_r_y_Pl_n"}, {"actionName": "Verify_Nut_an_ix_Leap_Recovery_Plan_Name"}, {"actionName": "Nut_an_ix_Assign_IPAddress_To_Network_Interface_Card"}, {"actionName": "Nut_an_ix_Leap_Recovery_Pl_n_Val_d_t_e_R_c_v_r_y_Pl_n_Tr_gt_Ste"}, {"actionName": "Nut_an_ix_Leap_Recovery_Pl_n_U_n_plan_d_Fail_o_v_r_Tr_gt_Site"}, {"actionName": "Nut_an_ix_Leap_Recovery_Plan_Planed_Fail_o_v_r_Tar_gt_Site"}, {"actionName": "Chk_Recovery_Point_Entity_VM_d_snot_ex_st_At_Tar_gt_L_cl_AZ"}, {"actionName": "Chk_Recovery_Point_Entity_VM_exist_At_Source_Failed_AZ"}, {"actionName": "Check_If_Network_Adapter_Exist_Using_NI_CI_PA_dd_res_s"}, {"actionName": "Check_If_Network_Adapter_Ex_st_Using_MAC_Address"}, {"actionName": "Assign_<PERSON><PERSON>_IPAddress_To_Windows_Server_randomly"}, {"actionName": "Assign_D<PERSON>_IPAddress_To_W_n_d_w_s_Sr_v_r_Using_MAC_Address"}, {"actionName": "Assign_D<PERSON>_IPAddress_To_W_n_d_w_Ser_v_r_Us_i_g_IPAddress"}, {"actionName": "Add_Network_Interface_Card_To_VM"}]}, {"name": "Nutanix", "actionLists": [{"actionName": "Verify_If_No_Rep_l_Is_Pending_Under_PDF_or_Inactive_Site"}, {"actionName": "Verify_If_No_Rep_l_Is_Pending_Under_PDF_or_Active_Site"}, {"actionName": "Migrate_Protection_Domain"}, {"actionName": "Execute_VM_Power_On"}, {"actionName": "Execute_VM_Power_Off"}, {"actionName": "Check_VM_State"}, {"actionName": "Check_VM_Exist_In_CG_Under_PDF_or_Inactive_Site"}, {"actionName": "Check_VM_Exist_In_CG_Under_PDF_or_Active_Site"}, {"actionName": "Check_Protection_Domain_Status"}, {"actionName": "Check_CG_Exist_In_PDF_or_Inactive_Site"}, {"actionName": "Check_CG_Exist_In_PDF_or_Active_Site"}]}]}, {"name": "PowerCLI", "workflowCategoryChildViewListVms": [{"name": "PowerCLI", "actionLists": [{"actionName": "Unmount_Data_Store"}, {"actionName": "U_n_mo_u_n_From_All_Es_xi_Host"}, {"actionName": "U_n_Register_VM"}, {"actionName": "U_n_Register_DATA_STORE"}, {"actionName": "Stop_VS_p_here_HA_Agent_Service"}, {"actionName": "Start_VS_p_here_HA_Agent_Service"}, {"actionName": "Rescan_HB_A_To_Find_New_Storage_LUN"}, {"actionName": "Register_VM_Without_ES_Xi_Host"}, {"actionName": "Register_VM"}, {"actionName": "Re_Scan_VM_F_Sf_or_Storage_LUN"}, {"actionName": "Power_On_VM"}, {"actionName": "Power_On_DATA_STORE"}, {"actionName": "Power_Off_VM"}, {"actionName": "Power_Off_DATA_STORE"}, {"actionName": "Mount_Data_store_On_All_Es_xi_Host"}, {"actionName": "Mount_Data_Store"}, {"actionName": "De_Attach_LUN"}, {"actionName": "Check_Data_Store_Can_Be_Unmount"}, {"actionName": "Attach_Lu_n_On_Multiple_ES_Xi_Host"}, {"actionName": "Attach_LUN"}]}]}, {"name": "VMWare", "workflowCategoryChildViewListVms": [{"name": "vCAV", "actionLists": [{"actionName": "v_APP_Update_Fail_over_Network_Settings"}, {"actionName": "v_APPS_et_Reverse_Replication"}, {"actionName": "v_APP_Power_On"}, {"actionName": "v_APP_Power_Off"}, {"actionName": "v_APP_Execute_Fail_Over"}, {"actionName": "v_APP_Delete"}, {"actionName": "v_APP_Check_Replication_State"}, {"actionName": "v_APP_Check_Recovery_State"}, {"actionName": "v_APP_Check_Overall_Health"}, {"actionName": "v_APP_Check_Fail_over_Network_Settings_Sr_c_T_gt"}, {"actionName": "v_APP_Check_Fail_over_Network_Settings"}, {"actionName": "VC_AVE_x_ecu_t_e_v_APPS_y_n_c"}]}, {"name": "RP4VM", "actionLists": [{"actionName": "Recover_Point_for_VM_Execute_Recover_Production"}, {"actionName": "Recover_Point_for_VM_Recovery_Activities_Recover_Production"}, {"actionName": "Recover_Point_for_VM_Test_Copy_For_Recover_Production_Stop_Activity"}, {"actionName": "Recover_Point_for_VM_Test_Copy_For_Recover_Production"}, {"actionName": "Recover_Point_for_VM_Execute_Fail_over_Role_Change"}, {"actionName": "Recover_Point_for_VM_Recovery_Activities_Fail_over_Role_Change"}, {"actionName": "Recover_Point_for_VM_Test_Copy_For_Fail_over_Stop_Activity"}, {"actionName": "Recover_Point_for_VM_Test_Copy_For_Fail_over"}, {"actionName": "Recover_Point_for_VM_Test_Copy_Stop_Activity"}, {"actionName": "Recover_Point_for_VM_Test_Copy"}, {"actionName": "Check_Consistency_Group_Transfer_Status_Active"}, {"actionName": "Check_Consistency_Group_Recovery_Activities_Status"}, {"actionName": "Check_Consistency_Group_Prod_Replica_v_RP_Ac_luster_Details"}, {"actionName": "Virtualization_RP_for_VM_Replication_Monitor"}]}, {"name": "Zerto", "actionLists": [{"actionName": "Execute_VP_GR_o_ll_back_Before_Move_Commit"}, {"actionName": "Execute_VP_GM_o_v_e_Commit_Policy_NONE"}, {"actionName": "Execute_VP_GM_o_v_e_Commit_without_Reverse_Protection"}, {"actionName": "Execute_VP_GM_o_v_e_Commit_with_Reverse_Protection"}, {"actionName": "Execute_VP_G_Fail_over_without_Commit_And_SD_Source_VM"}, {"actionName": "Check_VP_GS_tat_e_Volume_Initial_Sync"}, {"actionName": "Check_VP_GS_tat_e_Moving_Before_Commit"}, {"actionName": "Execute_VP_G_VP_G_Fail_over_Commit_with_Reverse_Protection"}, {"actionName": "Execute_VP_GS_TOP_FAIL_OVERT_EST"}, {"actionName": "Execute_VP_GR_o_ll_back_Before_Fail_over_Commit"}, {"actionName": "Check_VP_GP_rote_ct_ion_St_s_Meeting_S_LA_and_State_None"}, {"actionName": "Execute_VP_G_Fail_over_without_Commit"}, {"actionName": "Execute_VP_G_FAIL_OVERT_EST"}, {"actionName": "Check_VP_GS_tat_e_Delta_Syncing"}, {"actionName": "Check_VP_GR_e_cover_y_Site_DR"}, {"actionName": "Check_VP_GP_rote_ct_ion_State_Failing_Over_Rolling_Back"}, {"actionName": "Check_VP_GP_rote_ct_ion_State_Failing_Over_Before_Com"}, {"actionName": "Check_VP_GP_rote_ct_ion_Status_Failing_over"}]}]}, {"name": "vCenter", "workflowCategoryChildViewListVms": [{"name": "vCenter", "actionLists": [{"actionName": "RP_4_VM_Fail_over_Using_Predefined_Network"}, {"actionName": "RP_4_VM_Enable_Latest_Image"}, {"actionName": "RP_4_VM_Start_Replica_Transfer"}, {"actionName": "Add_Additional_Disk"}, {"actionName": "Verify_Volume_Mount"}, {"actionName": "Verify_Unmount_Status"}, {"actionName": "Verify_Storage_Lu_n"}, {"actionName": "Verify_Mount_Status"}, {"actionName": "Verify_Disk"}, {"actionName": "Verify_Device_Associated_To_Disk"}, {"actionName": "Verify_Attach_Status"}, {"actionName": "V_center_VM_Power_On"}, {"actionName": "V_center_VM_Power_Off"}, {"actionName": "V_center_VM_Check_Running"}, {"actionName": "V_center_Remove_VM"}, {"actionName": "V_center_Remove_Snapshot"}, {"actionName": "V_center_Provision_VM"}, {"actionName": "V_center_Power_On_VM"}, {"actionName": "V_center_Power_Off_VM"}, {"actionName": "V_center_Execute_VM_Command"}, {"actionName": "V_center_Create_Linked_Clone"}, {"actionName": "V_center_Check_VM_Tool_Status"}, {"actionName": "V_center_Check_VM_Power_State"}, {"actionName": "V_center_Check_VM_Exist"}, {"actionName": "VM_Unmount"}, {"actionName": "VM_U_n_Register"}, {"actionName": "VM_Remove_Snap_Shot"}, {"actionName": "VM_Register_Machine"}, {"actionName": "VM_Mount"}, {"actionName": "VM_Is_Available"}, {"actionName": "VM_Create_Snap_Shot"}, {"actionName": "VM_Check_Running"}, {"actionName": "Update_Windows_Host_Name"}, {"actionName": "Update_VM_Network_Info"}, {"actionName": "Rescan_All_Adapt_or_s"}, {"actionName": "Replicate_Virtual_Machine"}, {"actionName": "Remove_VM"}, {"actionName": "Remove_Lu_n_From_ES_XI"}, {"actionName": "Remove_Guest_VM_System_From_Domain"}, {"actionName": "Remove_Additional_Disk"}, {"actionName": "Power_On_Machine"}, {"actionName": "Power_Off_Machine"}, {"actionName": "Ne_p_App_VM_Unmount"}, {"actionName": "Mount_Using_VM_F_S_U_UI_D"}, {"actionName": "Join_Guest_VM_System_To_Domain"}, {"actionName": "Is_Machine_Running"}, {"actionName": "Execute_V_m_Ware_Power_CL_IC_om_man_d"}, {"actionName": "Execute_Power_CL_IS_cr_i_pt"}, {"actionName": "Execute_NC_he_Op_V_m_Ware_Power_CL_IC_om_man_d"}, {"actionName": "Execute_Check_VM_Command"}, {"actionName": "Detach_Lu_n_From_ES_XI"}, {"actionName": "Create_New_V_m"}, {"actionName": "Create_Linked_Clone"}, {"actionName": "Check_VM_Tool_Status"}, {"actionName": "Check_VM_Exist"}, {"actionName": "Change_Guest_VM_Host_Name"}, {"actionName": "Attach_Lu_n_s"}]}]}, {"name": "SRM", "workflowCategoryChildViewListVms": [{"name": "SRM", "actionLists": [{"actionName": "SRM_Re_Protect_Recovery_Plan"}, {"actionName": "SRM_Perform_Planned_Migration_Fail_over"}, {"actionName": "SRM_Perform_Disaster_Recovery_Fail_over"}, {"actionName": "SRM_Initiate_Recovery_Plan"}, {"actionName": "SRM_Execute_Test_Recovery_Plan"}, {"actionName": "SRM_Execute_Cleanup_Recovery_Plan"}, {"actionName": "SRM_Check_Recovery_Plan_State"}, {"actionName": "SRM_Check_Protection_Group_State"}, {"actionName": "Re_Product_Recovery_Plan"}, {"actionName": "Check_Recovery_Plan_State"}, {"actionName": "SRM_Monitor_Action"}]}]}]}, {"categoryName": "Nutanix", "workflowCategoryBaseChildViewListVms": [{"name": "Nutanix", "workflowCategoryChildViewListVms": [{"name": "Nutanix", "actionLists": [{"actionName": "Verify_Target_Local_Av_lab_l_t_y_Zone_for_Leap_R_c_v_r_y_Pl_n"}, {"actionName": "Verify_Source_Failed_Av_lb_l_t_y_Zone_for_Leap_R_c_v_r_y_Pl_n"}, {"actionName": "Verify_Nut_an_ix_Leap_Recovery_Plan_Name"}, {"actionName": "Nut_an_ix_Assign_IPAddress_To_Network_Interface_Card"}, {"actionName": "Nut_an_ix_Leap_Recovery_Pl_n_Val_d_t_e_R_c_v_r_y_Pl_n_Tr_gt_Ste"}, {"actionName": "Nut_an_ix_Leap_Recovery_Pl_n_U_n_plan_d_Fail_o_v_r_Tr_gt_Site"}, {"actionName": "Nut_an_ix_Leap_Recovery_Plan_Planed_Fail_o_v_r_Tar_gt_Site"}, {"actionName": "Chk_Recovery_Point_Entity_VM_d_snot_ex_st_At_Tar_gt_L_cl_AZ"}, {"actionName": "Chk_Recovery_Point_Entity_VM_exist_At_Source_Failed_AZ"}, {"actionName": "Check_If_Network_Adapter_Exist_Using_NI_CI_PA_dd_res_s"}, {"actionName": "Check_If_Network_Adapter_Ex_st_Using_MAC_Address"}, {"actionName": "Assign_<PERSON><PERSON>_IPAddress_To_Windows_Server_randomly"}, {"actionName": "Assign_D<PERSON>_IPAddress_To_W_n_d_w_s_Sr_v_r_Using_MAC_Address"}, {"actionName": "Assign_D<PERSON>_IPAddress_To_W_n_d_w_Ser_v_r_Us_i_g_IPAddress"}, {"actionName": "Add_Network_Interface_Card_To_VM"}]}, {"name": "Nutanix", "actionLists": [{"actionName": "Verify_If_No_Rep_l_Is_Pending_Under_PDF_or_Inactive_Site"}, {"actionName": "Verify_If_No_Rep_l_Is_Pending_Under_PDF_or_Active_Site"}, {"actionName": "Migrate_Protection_Domain"}, {"actionName": "Execute_VM_Power_On"}, {"actionName": "Execute_VM_Power_Off"}, {"actionName": "Check_VM_State"}, {"actionName": "Check_VM_Exist_In_CG_Under_PDF_or_Inactive_Site"}, {"actionName": "Check_VM_Exist_In_CG_Under_PDF_or_Active_Site"}, {"actionName": "Check_Protection_Domain_Status"}, {"actionName": "Check_CG_Exist_In_PDF_or_Inactive_Site"}, {"actionName": "Check_CG_Exist_In_PDF_or_Active_Site"}]}]}, {"name": "NutanixPDFailOver", "workflowCategoryChildViewListVms": [{"name": "NutanixPDFailOver", "actionLists": [{"actionName": "Vrfy_If_No_Replication_Pending_For_PD_In_Active_Site"}, {"actionName": "Verify_If_No_Replication_Pending_For_PD_Active_Site"}, {"actionName": "Protection_Domain_Activation_Fail_over_From_DR"}, {"actionName": "Chk_VM_Exist_In_Consistency_Gr_p_U_n_dr_PD_In_Act_v_e_Site"}, {"actionName": "Chk_VM_Exist_In_Consistency_Gr_p_U_n_dr_PD_Act_v_e_Ste"}, {"actionName": "Check_Protection_Domain_Status_For_Site"}, {"actionName": "Chk_Consist_n_c_y_Gr_p_Mem_br_Status_U_n_dr_PD_In_Act_v_e_Ste"}, {"actionName": "Chk_Consistency_Gr_p_Mem_br_Stats_U_n_dr_PD_Act_v_Ste"}, {"actionName": "Acknowledge_n_Resolve_R_c_n_t_Informative_Al_rt_For_PD"}]}]}]}, {"categoryName": "Virtualization", "workflowCategoryBaseChildViewListVms": [{"name": "Microsoft", "workflowCategoryChildViewListVms": [{"name": "HyperV", "actionLists": [{"actionName": "Verify_Disk_Status_On_Cluster_Shared_Volume"}, {"actionName": "Verify_Clstr_Shared_Volume_Disk_in_Fail_Over_Clstr"}, {"actionName": "Remove_Virtual_Sharing_Hard_Disk_Duplicate_VM"}, {"actionName": "Remove_Virtual_Sharing_Hard_Disk"}, {"actionName": "Enable_Virtual_Hard_Disk_Sharing_Optn_Duplicate_VM"}, {"actionName": "Enable_Virtual_Hard_Disk_Sharing_Option"}, {"actionName": "Disable_Virtual_Hard_Disk_Sharing_Optn_Duplcte_VM"}, {"actionName": "Disable_Virtual_Hard_Disk_Sharing_Option"}, {"actionName": "Chk_Virtual_HardDsk_Sharing_Optn_Status_Duplcte_VM"}, {"actionName": "Check_Virtual_Hard_Disk_Sharing_Option_Status"}, {"actionName": "Add_New_Virtual_Hard_Disk_Duplicate_VM"}, {"actionName": "Add_New_Virtual_Hard_Disk"}, {"actionName": "Hyper_VM_on_it_or_in_g"}, {"actionName": "Verify_Power_Off_Status_Of_Replica_PR"}, {"actionName": "Verify_Running_Status_Of_Primary_DR"}, {"actionName": "Start_Primary_Hyper_VVM_DR"}, {"actionName": "Verify_Replica_Replication_Mode_PR"}, {"actionName": "Verify_Hyper_V_Replication_State_PR"}, {"actionName": "Verify_Primary_Replication_Mode"}, {"actionName": "Verify_Hyper_V_Replication_State"}, {"actionName": "Rep_Mode_To_Primary_Rep_And_Start_Rep"}, {"actionName": "Verify_Fail_Over_Wait_Com_Status_Rep"}, {"actionName": "Fails_Over_Replica_On_Target_Host_DR"}, {"actionName": "Verify_Prepared_Fail_over_Status_PR"}, {"actionName": "Start_Fail_over_Primary_Rep_Pending"}, {"actionName": "Shutdown_Primary_Hyper_VO_ff"}, {"actionName": "Shutdown_Primary_Hyper_V"}, {"actionName": "Verify_Reverse_Rep_Connection_Configuration_DR"}, {"actionName": "Verify_Forward_Rep_Connection_Configuration_PR"}, {"actionName": "Verify_Primary_Replication_Mode_PR"}, {"actionName": "Verify_Replica_Replication_Mode_DR"}, {"actionName": "Verify_Hyper_V_Replication_State_DR"}]}, {"name": "Hyper V", "actionLists": [{"actionName": "Virtual_Switch_Verify_Exist_Status"}, {"actionName": "Verify_Reverse_Replication_Connection"}, {"actionName": "Verify_Forward_Replication_Connection"}, {"actionName": "Verify_Failed_Over_Waiting_Completion_Status"}, {"actionName": "VM_Network_Adapter_Verify_Status"}, {"actionName": "VM_Network_Adapter_Verify_V_LAN_IDS_tat_us"}, {"actionName": "Stop_VM"}, {"actionName": "Start_VM_Fail_Over"}, {"actionName": "Single_Net_Hyper_VVM_IPAddress_With_Out_Mac_Address"}, {"actionName": "Single_Net_Hyper_VVM_IPAddress_With_Mac_Address"}, {"actionName": "Set_VM_Reverse_Replication"}, {"actionName": "Set_V_LAN_ID_To_Virtual_Switch"}, {"actionName": "Set_Network_Adapter_Connection"}, {"actionName": "Prepare_VM_Fail_Over"}, {"actionName": "Execute_Check_Hyper_VC_om_man_d"}, {"actionName": "Disconnect_Network_Adapter_Connection"}, {"actionName": "Check_VM_State"}, {"actionName": "Check_VM_Replication_State"}, {"actionName": "Check_VM_Replication_Mode"}, {"actionName": "Check_Hyper_V_Replication"}, {"actionName": "Change_Hyper_VVM_IPAddress_With_Out_Mac_Address"}, {"actionName": "Change_Hyper_VVM_IPAddress_With_Mac_Address"}, {"actionName": "Cancel_Fail_Over_Standalone_Hyper_VVM"}]}]}, {"name": "Oracle_Solaris", "workflowCategoryChildViewListVms": [{"name": "ZONES", "actionLists": [{"actionName": "Shut_Down_Zone"}, {"actionName": "Detach_Zone"}, {"actionName": "Check_Zone_Up"}, {"actionName": "Check_Zone_Status"}, {"actionName": "Check_Zone_Down"}, {"actionName": "Boot_Zone"}, {"actionName": "Attach_Zone"}]}, {"name": "LDOM", "actionLists": [{"actionName": "Verify_LD_OM_Power_ON"}, {"actionName": "Verify_LD_OM_Power_OFF"}, {"actionName": "LD_OM_Unbind_Domain"}, {"actionName": "LD_OMS_top_Domain"}, {"actionName": "LD_OMS_tart_Domain"}, {"actionName": "LD_OM_Remove_Domain"}, {"actionName": "LD_OMB_ind_Domain"}, {"actionName": "LD_OM_ADD_Memory"}, {"actionName": "LD_OM_ADD_Domain"}, {"actionName": "LD_OM_ADD_CPU"}]}]}, {"name": "AIX", "workflowCategoryChildViewListVms": [{"name": "LPAR", "actionLists": [{"actionName": "AIX_LP_ARP_OWE_RON"}, {"actionName": "AIX_LP_ARP_OWE_R_OFF"}, {"actionName": "AIX_LP_ARCH_ECK_POWER_ON"}, {"actionName": "AIX_LP_ARCH_ECK_POWER_OFF"}]}]}]}], "DB": [{"categoryName": "Database", "workflowCategoryBaseChildViewListVms": [{"name": "Redis", "workflowCategoryChildViewListVms": [{"name": "RedisDBTableCountMatch", "actionLists": [{"actionName": "Red_is_Check_Empty_Array_DR"}, {"actionName": "Red_is_Check_Empty_Array_PR"}, {"actionName": "Red_is_Verify_Single_Table_Row_Count_DR"}, {"actionName": "Red_is_Verify_Single_Table_Row_Count_PR"}]}]}, {"name": "Postgres", "workflowCategoryChildViewListVms": [{"name": "NativeReplication", "actionLists": [{"actionName": "Post_gr_es_Start_Service"}, {"actionName": "Check_Content"}, {"actionName": "Add_Content_Recovery"}, {"actionName": "Check_Recovery_File_Primary"}, {"actionName": "Verify_Recovery_Done"}, {"actionName": "Restart_Service"}, {"actionName": "Create_Trigger_File"}, {"actionName": "Check_Recovery_File"}, {"actionName": "Post_gr_es_Stop_Service"}, {"actionName": "Verify_Recovery_Status"}, {"actionName": "Verify_Server_Ru_uni_n_g"}, {"actionName": "Verify_Post_Gr_es_SQL_Trigger_File_Name_And_Path"}, {"actionName": "Verify_Post_Gr_es_SQL_DR_Replication_Status"}, {"actionName": "Verify_Post_Gr_es_SQL_PR_Replication_Status"}, {"actionName": "Verify_Post_Gr_es_SQL_Database_Cluster_State"}, {"actionName": "Verify_Post_Gr_es_SQL_XL_o_g_LS_NM_at_ching"}, {"actionName": "Start_Post_gr_e_SQL_Server"}, {"actionName": "Restart_Post_gr_e_SQL_Server"}, {"actionName": "Execute_DR_Failing_Over_To_Standby_Server"}, {"actionName": "Verify_<PERSON>_Trigger_File_Path"}, {"actionName": "Verify_PR_DB_Cluster_Shutdown"}, {"actionName": "Stop_Post_gr_e_SQL_Server"}, {"actionName": "Create_Recovery_Conf_At_PR"}, {"actionName": "Verify_PR_And_DR_Wal_LS_NM_at_ch_es"}, {"actionName": "Verify_Database_Cluster_Status"}, {"actionName": "Execute_Post_gr_es_SQL_Command"}, {"actionName": "Cluster_Change_Over"}, {"actionName": "Promote_Cluster_State"}, {"actionName": "Verify_Replication_Status_DR"}, {"actionName": "Verify_Cluster_Status"}, {"actionName": "Verify_Post_gr_es_Service_Status"}, {"actionName": "Verify_Current_Wal_Location"}, {"actionName": "Verify_Post_gr_e_SQL_Recovery_Status"}, {"actionName": "Verify_Replication_Status_PR"}, {"actionName": "Verify_Cluster_State"}, {"actionName": "Post_gr_es_Monitoring"}, {"actionName": "PG_Recovery_Se_d"}, {"actionName": "Start_Post_gr_es_SQL_Service"}, {"actionName": "PG_Recovery_Cat"}, {"actionName": "Stop_Post_gr_e_SQL_Service"}, {"actionName": "PG_Rewind_Synchronize_Two_Cluster"}, {"actionName": "PG_Recovery_Conf_MV"}, {"actionName": "pg_rewind_test"}, {"actionName": "Create_Stand_By_File_In_Secondary_Ser"}, {"actionName": "Custom_Action_Test"}, {"actionName": "Power_Shell_Test"}, {"actionName": "Test_Action"}]}]}, {"name": "Sybase", "workflowCategoryChildViewListVms": [{"name": "DataSync", "actionLists": [{"actionName": "Start_S_y_base_Server"}, {"actionName": "Verify_S_y_base_Server_Is_Up"}, {"actionName": "Verify_S_y_base_Server_Is_Down"}, {"actionName": "S_y_base_Shut_Down_DB"}, {"actionName": "S_y_base_Check_Point_DB"}, {"actionName": "Replicate_S_y_base_B_cp_Table"}, {"actionName": "Dump_Transaction_Log_File_with_Standby_Access"}, {"actionName": "S_y_base_Online_DB_For_Standby_Access"}, {"actionName": "S_y_base_Online_DB"}, {"actionName": "Load_Transaction_Log_File"}, {"actionName": "Execute_S_y_base_IS_QL_Command"}, {"actionName": "Generate_S_y_base_Last_Transaction_Log"}, {"actionName": "Execute_S_y_base_B_cp_In_Command"}, {"actionName": "Execute_S_y_base_B_cp_Out_Command"}, {"actionName": "Verify_DB_Status"}, {"actionName": "Verify_Backup_Server_Status"}, {"actionName": "Verify_S_y_base_Data_Server_Status"}, {"actionName": "Copy_S_y_base_Last_Transaction_Log"}, {"actionName": "Execute_Check_S_y_base_IS_QL_command"}]}, {"name": "SRS", "actionLists": [{"actionName": "S_y_base_Replication_Resume_Connection"}, {"actionName": "S_y_base_Start_Replication_Agent_On_Primary"}, {"actionName": "S_y_base_Replication_Check_Role_Switch_Status"}, {"actionName": "S_y_base_Replication_Switch_Over_Status"}, {"actionName": "S_y_base_Primary_To_Standby"}, {"actionName": "Verify_S_y_base_Replication_Agent_IS_Down"}, {"actionName": "S_y_base_Stop_Replication_Agent_On_Primary"}, {"actionName": "Verify_S_y_base_Standby_DB_Status"}, {"actionName": "Verify_S_y_base_Primary_DB_Status"}]}]}, {"name": "MSSQL", "workflowCategoryChildViewListVms": [{"name": "commonIM", "actionLists": [{"actionName": "test_command_Copy_9011"}, {"actionName": "Test_WW_Copy_8328"}, {"actionName": "Verify_Database_Stat_u_Copy_3563"}, {"actionName": "LS_SQL_Rs_tr_e_Last_B_ck_up_Log_wt_h_No_R_c_v_r_y_With_Pr_ch_ck_Copy_1761"}, {"actionName": "LS_SQL_Enable_Logs_hipping_with_Target_DB_Restoring_Copy_2143"}, {"actionName": "LS_SQL_Restore_Last_log_with_Standby_DR_Copy_1751"}, {"actionName": "LS_SQL_Restore_Last_log_with_Recovery_DR_Copy_6317"}, {"actionName": "LS_SQL_Validate_Backup_with_Primary_Secondary_Copy_8706"}, {"actionName": "LS_SQL_Copy_Tail_Log_Backup_DR_Copy_4404"}, {"actionName": "LS_SQL_Verify_Primary_backup_transaction_log_Exist_Copy_4304"}, {"actionName": "LS_SQL_Execute_Secondary_Log_Shipping_Copy_6983"}, {"actionName": "LS_SQL_Execute_Primary_Log_Shipping_Reverse_Copy_6024"}, {"actionName": "Verify_Job_Name_Associated_With_Log_Shipping_Copy_9987"}, {"actionName": "Verify_Source_And_De_st_Backup_Directory_Copy_626"}, {"actionName": "Verify_Backup_Directory_and_Share_Path_Copy_2758"}, {"actionName": "Execute_2_DR_Site_Primary_Log_Shipping_Copy_3220"}, {"actionName": "LS_SQL_Restore_Last_Backup_Log_With_Recovery_Copy_8040"}, {"actionName": "Execute_3_Site_Secondary_Log_Shipping_Copy_7757"}, {"actionName": "Execute_3_DR_Site_Primary_Log_Shipping_Copy_2643"}, {"actionName": "Restore_Log_With_Standby_Copy_1265"}, {"actionName": "Verify_3_DR_Site_Log_File_Sequence_Copy_9302"}, {"actionName": "LS_SQL_Make_DB_Writable_Last_Restored_File_Fail_Over_Copy_1308"}, {"actionName": "LS_SQL_Execute_Secondary_Log_Shipping_Job_Schedule_Copy_8777"}, {"actionName": "LS_SQL_Execute_Primary_Log_Shipping_Job_Schedule_Copy_504"}, {"actionName": "SQL_NL_SC_heck_Database_Mode_Copy_5504"}, {"actionName": "SQL_NL_SC_heck_Database_State_Copy_5936"}, {"actionName": "Verify_Database_Mirroring_Status_Copy_8342"}, {"actionName": "Mirroring_Fail_Over_Copy_1092"}, {"actionName": "Kill_MSS_QL_Process_By_Name_Copy_4082"}, {"actionName": "ILS_SQL_Remove_Restore_Job_Copy_4676"}, {"actionName": "ILS_SQL_Remove_Copy_Job_Copy_8019"}, {"actionName": "Attach_DB_Copy_9781"}, {"actionName": "Set_DB_Option_Single_User_Copy_2035"}, {"actionName": "DB_Offline_Copy_5634"}, {"actionName": "LS_SQL_Migrate_Roles_Copy_5841"}, {"actionName": "ILS_SQL_Migrate_Logins_Copy_6748"}, {"actionName": "ILS_SQL_Execute_Secondary_Log_Shipping_Copy_8481"}, {"actionName": "ILS_SQL_Execute_Primary_Log_Shipping_Copy_1013"}, {"actionName": "ILS_SQL_Remove_Back_Up_Job_Copy_8703"}, {"actionName": "ILS_SQL_Run_Back_Up_Job_Copy_1312"}, {"actionName": "Linux_DisMount_Copy_8956"}, {"actionName": "Linux_Mount_Copy_1579"}, {"actionName": "ILS_SQL_Migrate_Roles_Copy_6694"}, {"actionName": "LS_SQL_Migrate_Logins_Copy_2225"}, {"actionName": "Detach_SQL_Database_Copy_6169"}, {"actionName": "Attach_SQL_Database_Copy_4977"}, {"actionName": "test_NL_SC_op_y_697"}, {"actionName": "LS_SQL_Remove_Secondary_Job_Copy_5188"}, {"actionName": "LS_SQL_Check_Db_Entry_On_Primary_Server_Exist_Copy_6825"}, {"actionName": "LS_SQL_Check_DB_Entry_On_Secondary_Server_Exist_Copy_3771"}, {"actionName": "LS_SQL_Check_Primary_Logs_hipping_Exist_Copy_1013"}, {"actionName": "LS_SQL_Check_Primary_Secondary_Logs_hipping_Exist_Copy_153"}, {"actionName": "LS_SQL_Restore_Secondary_Log_Shipping_Copy_6109"}, {"actionName": "LS_SQL_Restore_Primary_Log_Shipping_Copy_8024"}, {"actionName": "LS_SQL_Restore_Last_Back_Up_Log_With_No_Recovery_Copy_5534"}, {"actionName": "LS_SQL_Set_DB_Multi_User_Access_Mode_Copy_3724"}, {"actionName": "LS_SQL_Generate_Last_Back_Up_Log_Copy_8879"}, {"actionName": "LS_SQL_Kill_All_Session_Copy_6006"}, {"actionName": "MSS_QL_NL_SM_on_it_or_in_g_Copy_5990"}, {"actionName": "Fix_<PERSON><PERSON>n_Users_Copy_144"}, {"actionName": "ILS_SQL_Set_DR_DB_In_Multi_User_Access_Mode_Copy_7365"}, {"actionName": "LS_SQL_Updating_Restore_Job_With_DRIP_Address_Copy_7250"}, {"actionName": "ILS_SQL_Updating_Restore_Job_With_DRIP_Address_Copy_3921"}, {"actionName": "LS_SQL_Updating_Copy_Job_With_DRIP_Address_Copy_1697"}, {"actionName": "ILS_SQL_Updating_Copy_Job_With_DRIP_Address_Copy_7043"}, {"actionName": "ILS_SQL_Updating_Backup_Job_With_PR_IPAddress_Copy_6952"}, {"actionName": "LS_SQL_Updating_Backup_Job_With_PR_IPAddress_Copy_5431"}, {"actionName": "LS_SQL_Remove_Primary_Secondary_Log_Shipping_Copy_3349"}, {"actionName": "ILS_SQL_Remove_Primary_Log_Shipping_Copy_9087"}, {"actionName": "LS_SQL_Remove_Secondary_Log_Shipping_Copy_6454"}, {"actionName": "ILS_SQL_Remove_Secondary_Log_Shipping_Copy_9524"}, {"actionName": "LS_SQL_Remove_Primary_Log_Shipping_Copy_7851"}, {"actionName": "ILS_SQL_Remove_Primary_Secondary_Log_Shipping_Copy_3414"}, {"actionName": "LS_SQL_Restore_Database_With_Recovery_Copy_8869"}, {"actionName": "ILS_SQL_Restore_Database_With_Recovery_Copy_3308"}, {"actionName": "ILS_SQL_Set_DB_Single_User_Access_Mode_DR_Copy_2666"}, {"actionName": "ILS_SQL_Kill_Session_DR_Copy_1484"}, {"actionName": "ILS_SQL_Set_DB_Multi_User_Access_Mode_Primary_Copy_6753"}, {"actionName": "LS_SQL_Verify_DB_Single_User_Access_Mode_Copy_3803"}, {"actionName": "ILS_SQL_Verify_PR_DB_Single_User_Access_Mode_Copy_4681"}, {"actionName": "LS_SQL_Set_DB_Single_User_Access_Mode_Copy_9415"}, {"actionName": "ILS_SQL_Set_DB_Single_User_Access_Mode_Primary_Copy_4551"}, {"actionName": "Import_Login_Roles_On_DR_Server_Copy_5070"}, {"actionName": "Import_Logins_On_DR_Server_Copy_2279"}, {"actionName": "Export_Login_Roles_From_Production_Server_Copy_951"}, {"actionName": "Export_Login_From_Production_Server_Copy_8248"}, {"actionName": "ILS_SQL_Kill_Session_Primary_Copy_3833"}, {"actionName": "LS_SQL_Fix_Orphan_Users_Copy_4095"}, {"actionName": "LS_SQL_Enable_Job_Copy_978"}, {"actionName": "LS_SQL_Generate_Last_Back_Up_Log_With_No_Recovery_Copy_9108"}, {"actionName": "LS_SQL_Kill_DB_Session_With_Time_Out_Copy_8578"}, {"actionName": "LS_SQL_Run_Job_Copy_7046"}, {"actionName": "LS_SQL_Disable_Job_Copy_903"}, {"actionName": "Verify_Log_File_Sequence_Copy_2486"}, {"actionName": "<PERSON>_<PERSON><PERSON>_Job_Copy_5153"}, {"actionName": "Run_<PERSON><PERSON>_Job_Copy_7219"}, {"actionName": "Disable_<PERSON><PERSON>_Job_Copy_9208"}, {"actionName": "Disable_<PERSON><PERSON>_Job_Copy_5245"}, {"actionName": "Disable_Back_Up_Job_Copy_5566"}, {"actionName": "Backup_Job_Status_Copy_9896"}, {"actionName": "Transaction_Log_Shipping_State_Copy_5868"}, {"actionName": "Database_Access_Mode_Copy_5213"}, {"actionName": "Database_Recovery_Model_Copy_1968"}, {"actionName": "Verify_Database_Recovery_Mode_Copy_4754"}, {"actionName": "Verify_Update_ability_State_DR_Copy_9100"}, {"actionName": "Verify_Update_ability_State_PRC_op_y_8009"}, {"actionName": "Verify_Database_Status_Copy_2448"}]}, {"name": "Common", "actionLists": [{"actionName": "Execute_MSS_QL_Command"}, {"actionName": "Execute_Check_MSS_QL_Command"}, {"actionName": "Compare_SQL_Server_Table_Row_Count_DR"}, {"actionName": "Compare_SQL_Server_Table_Row_Count_PR"}]}, {"name": "MSSQL AlwaysONPauseResume", "actionLists": [{"actionName": "U_n_joined_DB_from_Availability_Group"}, {"actionName": "Restore_Database_with_Recovery_DR_Only"}, {"actionName": "Remove_Primary_Database"}, {"actionName": "Modify_AG_Mode"}, {"actionName": "Join_Secondary_DB_to_Availability_Group"}, {"actionName": "Check_DB_is_Joined_or_U_n_joined"}, {"actionName": "Add_Primary_Database_to_Avail_ab_ii_t_y_Group"}]}, {"name": "MSSQL AlwaysONFailover", "actionLists": [{"actionName": "Testa_ll_Availability_Replicas_Health_Prod"}, {"actionName": "Testa_ll_Availability_Replicas_Health_DR"}, {"actionName": "Syn_Always_On_Fail_over_With_Specific_Secondary"}, {"actionName": "Syn_Always_On_Fail_over_With_Random_Secondary"}, {"actionName": "Suspend_Replication_All_DB_in_Availability_Gr_up"}, {"actionName": "Resume_Replication"}, {"actionName": "Manual_Planned_Fail_over"}, {"actionName": "Force_Fully_Fail_over"}, {"actionName": "Check_Suspended_State"}, {"actionName": "Check_Resumed"}, {"actionName": "Check_Availability_Group_Status_Prod"}, {"actionName": "Check_Availability_Group_Status_Enable_Disable"}, {"actionName": "Check_Availability_Group_Status_DR"}, {"actionName": "Check_Allow_All_Connections_Prod_DR"}, {"actionName": "Check_All_Databases_State"}, {"actionName": "AS_y_n_Always_On_Fail_over_With_Specific_Secondary"}, {"actionName": "AS_y_n_Always_On_Fail_over_With_Random_Secondary"}]}, {"name": "DatabaseMirroring", "actionLists": [{"actionName": "Check_Sq_l_Server_Running_States"}, {"actionName": "Check_DB_Role_And_Mirror_State"}, {"actionName": "Execute_DB_Cluster_Change_Over"}, {"actionName": "MSS_QL_Mirroring_Monitoring"}]}, {"name": "AlwaysONAvailabilityGroup", "actionLists": [{"actionName": "Change_Cluster_Node_Weight_Multiple"}, {"actionName": "Change_Cluster_Node_Weight_Single"}, {"actionName": "Check_Cluster_Node_Weight_Multiple"}, {"actionName": "Check_Cluster_Node_Weight_Single"}, {"actionName": "Cluster_Name_Associate_with_Group"}]}, {"name": "DataSync", "actionLists": [{"actionName": "Sq_l_D_SM_s_Sq_l_Restore_Last_Log"}, {"actionName": "Sq_l_D_SM_i_grate_Server_Roles_DR"}, {"actionName": "Sq_l_D_SM_SS_ql_Restore_Log"}, {"actionName": "Scan_File_From_Application_Server"}, {"actionName": "SQL_D_SM_i_grate_Server_Roles_PR"}, {"actionName": "SQL_D_SM_i_grate_Logging_PR"}, {"actionName": "SQL_D_SM_i_grate_Logging_DR"}, {"actionName": "SQL_D_SM_SS_QL_Restore_DB_With_Recovery"}, {"actionName": "SQL_D_SM_SS_QL_Kill_Process_Sec"}, {"actionName": "SQL_D_SM_SS_QL_Kill_Process_Prim"}, {"actionName": "SQL_D_SM_SS_QL_Generate_Last_Log_F_C"}, {"actionName": "SQL_D_SM_SS_QL_DB_Option"}, {"actionName": "SQL_D_SD_at_Sync_SQL_2000"}, {"actionName": "Move_CS_VF_i_l_e_To_DB_Server"}, {"actionName": "File_Replication_Application_To_DB_Server"}, {"actionName": "Download_Response_File_From_Production_Server"}, {"actionName": "Create_CS_VF_i_l_e_From_RESP"}, {"actionName": "Application_Data_Sync"}]}, {"name": "NativeLogShipping", "actionLists": [{"actionName": "MSS_QL_NL_SM_on_it_or_in_g"}, {"actionName": "Fix_Orphan_Users"}, {"actionName": "ILS_SQL_Set_DR_DB_In_Multi_User_Access_Mode"}, {"actionName": "LS_SQL_Updating_Restore_Job_With_DRIP_Address"}, {"actionName": "ILS_SQL_Updating_Restore_Job_With_DRIP_Address"}, {"actionName": "LS_SQL_Updating_Copy_Job_With_DRIP_Address"}, {"actionName": "ILS_SQL_Updating_Copy_Job_With_DRIP_Address"}, {"actionName": "ILS_SQL_Updating_Backup_Job_With_PR_IPAddress"}, {"actionName": "LS_SQL_Updating_Backup_Job_With_PR_IPAddress"}, {"actionName": "LS_SQL_Remove_Primary_Secondary_Log_Shipping"}, {"actionName": "ILS_SQL_Remove_Primary_Log_Shipping"}, {"actionName": "LS_SQL_Remove_Secondary_Log_Shipping"}, {"actionName": "ILS_SQL_Remove_Secondary_Log_Shipping"}, {"actionName": "LS_SQL_Remove_Primary_Log_Shipping"}, {"actionName": "ILS_SQL_Remove_Primary_Secondary_Log_Shipping"}, {"actionName": "LS_SQL_Restore_Database_With_Recovery"}, {"actionName": "ILS_SQL_Restore_Database_With_Recovery"}, {"actionName": "ILS_SQL_Set_DB_Single_User_Access_Mode_DR"}, {"actionName": "ILS_SQL_Kill_Session_DR"}, {"actionName": "ILS_SQL_Set_DB_Multi_User_Access_Mode_Primary"}, {"actionName": "LS_SQL_Verify_DB_Single_User_Access_Mode"}, {"actionName": "ILS_SQL_Verify_PR_DB_Single_User_Access_Mode"}, {"actionName": "LS_SQL_Set_DB_Single_User_Access_Mode"}, {"actionName": "ILS_SQL_Set_DB_Single_User_Access_Mode_Primary"}, {"actionName": "Import_Login_Roles_On_DR_Server"}, {"actionName": "Import_Logins_On_DR_Server"}, {"actionName": "Export_Login_Roles_From_Production_Server"}, {"actionName": "Export_Login_From_Production_Server"}, {"actionName": "ILS_SQL_Kill_Session_Primary"}, {"actionName": "LS_SQL_Fix_Orphan_Users"}, {"actionName": "LS_SQL_Enable_Job"}, {"actionName": "LS_SQL_Generate_Last_Back_Up_Log_With_No_Recovery"}, {"actionName": "LS_SQL_Kill_DB_Session_With_Time_Out"}, {"actionName": "LS_SQL_Run_Job"}, {"actionName": "LS_SQL_Disable_Job"}, {"actionName": "Verify_Log_File_Sequence"}, {"actionName": "Run_Restore_Job"}, {"actionName": "Run_Co<PERSON>_Job"}, {"actionName": "Disable_Restore_Job"}, {"actionName": "Disable_Co<PERSON>_Job"}, {"actionName": "Disable_Back_Up_Job"}, {"actionName": "Backup_Job_Status"}, {"actionName": "Transaction_Log_Shipping_State"}, {"actionName": "Database_Access_Mode"}, {"actionName": "Database_Recovery_Model"}, {"actionName": "LS_SQL_Restore_Secondary_Log_Shipping"}, {"actionName": "LS_SQL_Restore_Primary_Log_Shipping"}, {"actionName": "LS_SQL_Generate_Last_Back_Up_Log"}, {"actionName": "LS_SQL_Kill_All_Session"}, {"actionName": "LS_SQL_Restore_Last_Back_Up_Log_With_No_Recovery"}, {"actionName": "LS_SQL_Set_DB_Multi_User_Access_Mode"}, {"actionName": "LS_SQL_Remove_Secondary_Job"}, {"actionName": "LS_SQL_Check_Db_Entry_On_Primary_Server_Exist"}, {"actionName": "LS_SQL_Check_DB_Entry_On_Secondary_Server_Exist"}, {"actionName": "LS_SQL_Check_Primary_Logs_hipping_Exist"}, {"actionName": "LS_SQL_Check_Primary_Secondary_Logs_hipping_Exist"}, {"actionName": "Verify_Database_Recovery_Mode"}, {"actionName": "Verify_Update_ability_State_DR"}, {"actionName": "Verify_Update_ability_State_PR"}, {"actionName": "Verify_Database_Status"}, {"actionName": "test_NL_S"}, {"actionName": "LS_SQL_Rs_tr_e_Last_B_ck_up_Log_wt_h_No_R_c_v_r_y_With_Pr_ch_ck"}, {"actionName": "LS_SQL_Enable_Logs_hipping_with_Target_DB_Restoring"}, {"actionName": "LS_SQL_Restore_Last_log_with_Standby_DR"}, {"actionName": "LS_SQL_Restore_Last_log_with_Recovery_DR"}, {"actionName": "LS_SQL_Validate_Backup_with_Primary_Secondary"}, {"actionName": "LS_SQL_Copy_Tail_Log_Backup_DR"}, {"actionName": "LS_SQL_Verify_Primary_backup_transaction_log_Exist"}, {"actionName": "LS_SQL_Execute_Secondary_Log_Shipping"}, {"actionName": "LS_SQL_Execute_Primary_Log_Shipping_Reverse"}, {"actionName": "Verify_Job_Name_Associated_With_Log_Shipping"}, {"actionName": "Verify_Source_And_De_st_Backup_Directory"}, {"actionName": "Verify_Backup_Directory_and_Share_Path"}, {"actionName": "Execute_2_DR_Site_Primary_Log_Shipping"}, {"actionName": "LS_SQL_Restore_Last_Backup_Log_With_Recovery"}, {"actionName": "Execute_3_Site_Secondary_Log_Shipping"}, {"actionName": "Execute_3_DR_Site_Primary_Log_Shipping"}, {"actionName": "Restore_Log_With_Standby"}, {"actionName": "Verify_3_DR_Site_Log_File_Sequence"}, {"actionName": "LS_SQL_Make_DB_Writable_Last_Restored_File_Fail_Over"}, {"actionName": "LS_SQL_Execute_Secondary_Log_Shipping_Job_Schedule"}, {"actionName": "LS_SQL_Execute_Primary_Log_Shipping_Job_Schedule"}, {"actionName": "SQL_NL_SC_heck_Database_Mode"}, {"actionName": "SQL_NL_SC_heck_Database_State"}, {"actionName": "Verify_Database_Mirroring_Status"}, {"actionName": "Mirroring_Fail_Over"}, {"actionName": "Kill_MSS_QL_Process_By_Name"}, {"actionName": "ILS_SQL_Remove_Restore_Job"}, {"actionName": "ILS_SQL_Remove_Copy_Job"}, {"actionName": "Attach_DB"}, {"actionName": "Set_DB_Option_Single_User"}, {"actionName": "DB_Offline"}, {"actionName": "LS_SQL_Migrate_Roles"}, {"actionName": "ILS_SQL_Migrate_Logins"}, {"actionName": "ILS_SQL_Execute_Secondary_Log_Shipping"}, {"actionName": "ILS_SQL_Execute_Primary_Log_Shipping"}, {"actionName": "ILS_SQL_Remove_Back_Up_Job"}, {"actionName": "ILS_SQL_Run_Back_Up_Job"}, {"actionName": "Linux_DisMount"}, {"actionName": "Linux_Mount"}, {"actionName": "ILS_SQL_Migrate_Roles"}, {"actionName": "LS_SQL_Migrate_Logins"}, {"actionName": "Detach_SQL_Database"}, {"actionName": "Attach_SQL_Database"}, {"actionName": "LS_SQL_Verify_Db_Entry_On_Primary_Server_Exist"}, {"actionName": "LS_SQL_Verify_DB_Entry_On_Secondary_Server_Exist"}, {"actionName": "LS_SQL_Verify_Primary_Logs_hipping_Exist"}, {"actionName": "LS_SQL_Verify_Primary_Secondary_Logs_hipping_Exist"}, {"actionName": "SQL_NL_SC_heck_Database_Mode"}]}, {"name": "AlwaysON", "actionLists": [{"actionName": "Change_Availability_Mode"}, {"actionName": "Execute_Always_On_Normal_Fail_Over"}, {"actionName": "Pr_e_Flight_Check_Database_Sync_State"}, {"actionName": "Always_ON_Mont_i_or_in_g"}, {"actionName": "Resume_Data_Movement"}, {"actionName": "Execute_Always_On_Force_Fail_Over"}, {"actionName": "Check_Availability_Mode"}, {"actionName": "Check_Role"}]}]}, {"name": "MongoDB", "workflowCategoryChildViewListVms": [{"name": "NativeReplication", "actionLists": [{"actionName": "Check_Priority_State_Custom"}, {"actionName": "Check_Health_Status_UP"}, {"actionName": "Mongo_DB_Monitor"}, {"actionName": "Check_Replication_Lag_Status"}, {"actionName": "Set_Priority_Of_The_Replica_Set"}, {"actionName": "Check_Mango_DB_Primary_State"}, {"actionName": "Check_Secondary_State"}]}]}, {"name": "IBM_DB2", "workflowCategoryChildViewListVms": [{"name": "IBM_HADR_for_LUW", "actionLists": [{"actionName": "DB_2_Take_Over_HAD_R"}, {"actionName": "DB_2_Start"}, {"actionName": "DB_2_De_Activate_DB_Start"}, {"actionName": "DB_2_Activate_DB"}, {"actionName": "DB_2_Switch_Back_DB"}, {"actionName": "DB_2_Switch_Over_DB"}, {"actionName": "DB_2_Is_DB_Up"}, {"actionName": "DB_2_Is_HAD_RA_ct_iv_e"}, {"actionName": "DB_2_Stop_HAD_R"}, {"actionName": "DB_2_Start_HAD_RP_rim_ar_y"}, {"actionName": "DB_2_De_Activate_DB_Terminate"}, {"actionName": "DB_2_Start_HAD_RS_tan_dB_y"}, {"actionName": "DB_2_Deactivate_Database"}, {"actionName": "DB_2_Terminate"}, {"actionName": "DB_2_Is_Database_Active"}, {"actionName": "DB_2_U_n_quiesce_Database"}, {"actionName": "DB_2_Is_Database_Quiesced"}, {"actionName": "DB_2_Quiesce_Database"}, {"actionName": "DB_2_Verify_Log_Gap"}, {"actionName": "DB_2_Verify_Log_Position"}, {"actionName": "DB_2_Is_HAD_RS_tat_e_PEER"}, {"actionName": "DB_2_<PERSON>_HAD_R_Role_Standby"}, {"actionName": "DB_2_Is_HAD_R_Role_Primary"}, {"actionName": "DB_2_Is_Database_Standby"}, {"actionName": "DB_2_Is_Database_Primary"}, {"actionName": "DB_2_HAD_RM_on_it_or_in_g"}]}]}, {"name": "Hypervisor", "workflowCategoryChildViewListVms": [{"name": "HyperVCluster", "actionLists": [{"actionName": "Verify_status_of_VM"}, {"actionName": "Verify_Failed_Over_Waiting_Completion_Status"}, {"actionName": "Verify_State"}, {"actionName": "Verify_<PERSON><PERSON>_Repli_Connection_config"}, {"actionName": "Verify_Replication_Mode"}, {"actionName": "Verify_Replication_State"}, {"actionName": "Verify_Prepared_For_Fail_overs_tat_us"}, {"actionName": "Verify_Hyper_VVM_V_LAN_ID_Connected_Status"}, {"actionName": "Verify_Hyper_VVM_DisConnected_Status"}, {"actionName": "Verify_Hyper_VVM_Connected_Status"}, {"actionName": "Verify_Forward_Repli_Connection_config"}, {"actionName": "Start_VM"}, {"actionName": "Start_Fail_over"}, {"actionName": "Single_Net_Clster_Hypr_VVM_IPAddress_Wth_Mac_Address"}, {"actionName": "Shutdown_VM"}, {"actionName": "Set_V_LAN_ID_To_Virtual_Switch_On_Hyper_VVM"}, {"actionName": "Hyper_V_Resume_Replication"}, {"actionName": "Fails_Over_Replica_VM"}, {"actionName": "Disconnection_To_Hyper_VVM"}, {"actionName": "Connection_To_Virtual_Switch_On_Hyper_VVM"}, {"actionName": "Cluster_Verify_Primary_Replication_Mode"}, {"actionName": "Change_Replication_mode"}, {"actionName": "Change_DNS_IP"}, {"actionName": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Hypr_VVM_IPAddress_Wth_Out_Mac_Address"}, {"actionName": "Change_Cluster_Hyper_VVM_IPAddress_With_Mac_Address"}, {"actionName": "Cancel_Fail_Over_Cluster_Hyper_VVM"}, {"actionName": "Single_Net_Clster_Hypr_VVM_IPAddress_Wth_uy_Mac_Adres"}, {"actionName": "Hyper_VM_on_it_or_in_g_Action"}]}, {"name": "MSSQLCluster", "actionLists": [{"actionName": "Rmove_Clstr_Resorce_Dpndncy_Frm_Dpndnt_Resorce"}, {"actionName": "Add_Cluster_Resource_Dependency_To_D_p_n_dent_Resorce"}, {"actionName": "Verify_Cluster_Resource_Dpndncy_In_Dependent_Rs_orc_e"}, {"actionName": "Remove_Cluster_Resource_From_Cluster_Owner_Group"}, {"actionName": "Add_Cluster_Resource_To_Cluster_Owner_Group"}]}]}, {"name": "MySQL", "workflowCategoryChildViewListVms": [{"name": "NativeReplication", "actionLists": [{"actionName": "<PERSON><PERSON><PERSON>_Master_Log_File_And_Post_ion_on_Ms_tr_Slave_Server"}, {"actionName": "Stop_Slave"}, {"actionName": "Slave_SQL_Running_Status"}, {"actionName": "Slave_IO_State"}, {"actionName": "Slave_IO_Running_Status"}, {"actionName": "Relay_Master_Log_File"}, {"actionName": "Read_Master_Log_Position"}, {"actionName": "My_SQL_Slave_Status"}, {"actionName": "My_SQL_Services_tat_us"}, {"actionName": "Master_Relay_Master_log_File"}, {"actionName": "Master_Read_Master_log_Position"}, {"actionName": "Master_Log_File"}, {"actionName": "Execute_Mys_ql_DB_Command"}, {"actionName": "Execute_Check_Mys_ql_DB_Command"}, {"actionName": "Exec_Master_Log_Position"}, {"actionName": "Connect_Status"}, {"actionName": "Change_MASTER_To_Master_Host_And_Log_File"}, {"actionName": "Change_Master_To_Master_Host"}, {"actionName": "Set_Global_Read_Only_OFF"}, {"actionName": "Set_Global_Read_Only_ON"}, {"actionName": "Flush_Logs"}, {"actionName": "Flush_Tables"}, {"actionName": "Verify_Read_Write_On_Master_Instance"}, {"actionName": "Make_Master_Instance_To_Read_Write"}, {"actionName": "Show_Master_Status"}, {"actionName": "Start_Slave_In_Master"}, {"actionName": "Check_Slave_Stopped_Status"}, {"actionName": "My_SQL_Monitoring"}, {"actionName": "Stop_Replication_In_Slave_Server"}, {"actionName": "Make_Master_Instance_Read_Only"}, {"actionName": "Verify_Read_Only_On_Slave"}, {"actionName": "Verify_Rep_User_Connectivity_From_Slave_To_Master"}, {"actionName": "Verify_Read_Only_On_Master"}, {"actionName": "Verify_SQL_Log_Bin_Set_To_ON_Master"}, {"actionName": "Verify_Read_Only_On_Master_Instance"}, {"actionName": "Verify_SQL_Log_Position_Master_And_Slave_Server"}]}]}, {"name": "ASM Oracle", "workflowCategoryChildViewListVms": [{"name": "DataSync", "actionLists": [{"actionName": "Recover_Standby_DB"}, {"actionName": "Pr_e_Shut_DB"}, {"actionName": "PR_Pr_e_Shut_Redo_Ctr_l_Script"}, {"actionName": "Mount_Standby_Database"}, {"actionName": "Create_Control_Script"}, {"actionName": "Create_Control_File_From_Trace_File"}, {"actionName": "Backup_Redo_Control_File"}, {"actionName": "Add_Temp_Table_Space"}, {"actionName": "AS_MR_est_ore_Standby_Control_File"}, {"actionName": "AS_MR_e_cover_Database"}, {"actionName": "AS_MO_pen_Database"}, {"actionName": "AS_MC_re_ate_Standby_Control_File"}, {"actionName": "Start_Up_No_Mount"}, {"actionName": "Start_Up_Mount"}, {"actionName": "Shut_Down_Standby_DB"}, {"actionName": "Shut_Down_Primary_DB"}, {"actionName": "Shut_own_DR_DB"}]}]}, {"name": "OracleRsync", "workflowCategoryChildViewListVms": [{"name": "OracleRsync", "actionLists": [{"actionName": "Replicate_RS_y_n_c_Folders_Posix"}, {"actionName": "Replicate_RS_y_n_c_File_Posix"}]}, {"name": "OracleRsync", "actionLists": [{"actionName": "Ora_Rs_y_n_c_Replicate_Stand_By_Control_File"}, {"actionName": "Ora_Rs_y_n_c_Replicate_Stand_By_Trace_File"}]}]}, {"name": "Generic Database Actions", "workflowCategoryChildViewListVms": [{"name": "Generic Database Actions", "actionLists": [{"actionName": "Update_DR_Operation_Status"}]}]}, {"name": "MaxDB", "workflowCategoryChildViewListVms": [{"name": "MaxDB", "actionLists": [{"actionName": "Verify_Database_State"}, {"actionName": "Max_DB_Start_Database"}, {"actionName": "Max_DB_Stop_Database"}, {"actionName": "Execute_Check_Command"}]}]}, {"name": "Oracle", "workflowCategoryChildViewListVms": [{"name": "DGBroker", "actionLists": [{"actionName": "DG_B_Validate_DB"}, {"actionName": "DG_BS_witch_Over"}, {"actionName": "DG_BF_ail_Over"}, {"actionName": "DG_BC_on_vert_Snapshot_Standby"}, {"actionName": "DG_BC_on_vert_Physical_Standby"}, {"actionName": "DG_BC_on_figuration_Is_OK"}]}, {"name": "ODG12C", "actionLists": [{"actionName": "Oracle_DG_12_CS_tart_Recovery"}, {"actionName": "Oracle_DG_12_CS_tart_DB"}, {"actionName": "Oracle_DG_12_CS_hut_down_DB"}, {"actionName": "Oracle_DG_12_CM_o_u_n_t_DB"}, {"actionName": "Oracle_DG_12_CD_G_Verify_Switchover"}, {"actionName": "Oracle_DG_12_CD_GS_witch_over"}]}, {"name": "WebLogic", "actionLists": [{"actionName": "Stop_Node_Manager_Service"}, {"actionName": "Stop_Managed_Server"}, {"actionName": "Stop_HTTP_Server"}, {"actionName": "Stop_Admin_Server"}, {"actionName": "Start_Node_Manager_Service"}, {"actionName": "Start_Managed_Server"}, {"actionName": "Start_HTTP_Server"}, {"actionName": "Start_Admin_Server"}]}, {"name": "DataGuard", "actionLists": [{"actionName": "Verify_DB_Mode_After_Revert_Snapshot"}, {"actionName": "Verify_DB_Role_After_Revert_Snapshot"}, {"actionName": "Verify_DB_Role_Be_for_<PERSON><PERSON>_e_Snapshot"}, {"actionName": "Verify_DB_Role_Be_for_Snapshot"}, {"actionName": "Check_Flash_Back_Off"}, {"actionName": "Convert_Snap_To_Physical_Standby"}, {"actionName": "Verify_DB_Role_After_Snapshot"}, {"actionName": "Verify_DB_Mode_After_Snapshot"}, {"actionName": "Verify_Current_Sc_n"}, {"actionName": "Convert_Snapshot_Standby"}, {"actionName": "Verify_DB_Flash_Back_Retention_Target"}, {"actionName": "Verify_DB_Recovery_File_De_st_Size"}, {"actionName": "Verify_DB_Recovery_File_De_st"}, {"actionName": "Verify_Data_Guard_Status"}, {"actionName": "Verify_DB_Mode_Be_for_Snapshot"}, {"actionName": "Verify_DB_Mode_Be_for_<PERSON><PERSON>_Snapshot"}, {"actionName": "DG_Win_Verify_Max_Sequence_Number"}, {"actionName": "DG_Database_Open"}, {"actionName": "DG_Shut_Down_Primary"}, {"actionName": "DG_Connector"}, {"actionName": "Execute_Sq_l_Command"}, {"actionName": "While_Check"}, {"actionName": "DG_Mount_Standby"}, {"actionName": "DG_Job_Status"}, {"actionName": "Test_Chat_bot"}, {"actionName": "Test_Action_Role_and_Mode"}, {"actionName": "s_r_v_ct_l_Start_Instance_OO_pen"}, {"actionName": "OD_GM_on_it_or_Action"}, {"actionName": "Alter_Data_Base_Switch_Over"}, {"actionName": "Verify_<PERSON>er_DB_SO"}, {"actionName": "s_r_v_ct_l_Stop_Database"}, {"actionName": "s_r_v_ct_l_Start_Database"}, {"actionName": "s_r_v_ct_l_Stop_Instance"}, {"actionName": "s_r_v_ct_l_Start_Instance_OM_o_u_n_t"}, {"actionName": "Check_Open_Mode"}, {"actionName": "<PERSON>er_DB_Mount"}, {"actionName": "Start_Up_No_Mount"}, {"actionName": "Switch_Over_Status"}, {"actionName": "Execute_Check_Sq_l_Command"}, {"actionName": "DG_Switch_Stand_By_To_Primary"}, {"actionName": "DG_Switch_Primary_To_Stand_By"}, {"actionName": "DG_Switch_Log_File"}, {"actionName": "DG_Recover_Stand_By"}, {"actionName": "DG_Check_User_Status"}, {"actionName": "DG_Verify_Max_Sequence_Number"}, {"actionName": "DG_Verify_DB_Mode_And_Role"}]}, {"name": "Rac 12c", "actionLists": [{"actionName": "DG_Switch_Log_File_Current"}, {"actionName": "Job_Status_In_PR"}, {"actionName": "Recovery_Start_In_New_DR"}, {"actionName": "Shut_Down_DR"}, {"actionName": "Mount_New_DR_DB_Stand_By"}, {"actionName": "Shut_Down_Primary"}, {"actionName": "Verify_Archive_Log_Sequence"}, {"actionName": "Verify_Switchover_Status"}, {"actionName": "Oracle_RA_CM_on_it_or_in_g_Action"}]}, {"name": "DataGuardAPI", "actionLists": [{"actionName": "Oracle_Data_Guard_APIS_witch_DB"}, {"actionName": "Oracle_Data_Guard_API_Check_DB_Sync_Status"}]}, {"name": "DataSync", "actionLists": [{"actionName": "Verify_Log_Sequence"}, {"actionName": "Switch_Shut_Primary_DB"}, {"actionName": "Switch_Shut_PR_DB_Win_D_S"}, {"actionName": "Switch_Shut_PR_DB_Win"}, {"actionName": "Stop_Oracle_Listener"}, {"actionName": "Stop_Listener_With_Password"}, {"actionName": "Start_Up_Mount_Force"}, {"actionName": "Start_Oracle_Listener"}, {"actionName": "Start_Listener_With_Password"}, {"actionName": "Start_Database_No_Mount"}, {"actionName": "Start_Database_Mount"}, {"actionName": "Start_Database"}, {"actionName": "Start_Data_Base_Read_Only"}, {"actionName": "Start_DB_Stand_By_Win_D_S"}, {"actionName": "Start_DB_Stand_By_Win"}, {"actionName": "Start_DB_Stand_By"}, {"actionName": "Start_DB_Read_Write_Win_D_S"}, {"actionName": "Start_DB_Read_Write_Win"}, {"actionName": "Start_DB_Read_Write"}, {"actionName": "Shut_Stand_By_DB_Win_D_S"}, {"actionName": "Shut_Down_Data_Base"}, {"actionName": "Shut_DB"}, {"actionName": "Restore_Standby_Control_File"}, {"actionName": "Replicate_Stand_By_Control_File"}, {"actionName": "Recover_Stand_By_Database_D_S"}, {"actionName": "Recover_Standby_Database_File"}, {"actionName": "Recover_Database"}, {"actionName": "Pr_e_Shut_Redo_Ctr_l_Script"}, {"actionName": "Open_Database"}, {"actionName": "Kill_Oracle_Sessions"}, {"actionName": "Is_Check_Point_Count_One"}, {"actionName": "Generate_Redo_Ctr_l_BK_PS_cr_i_pt_Win_D_S"}, {"actionName": "Generate_Redo_Ctr_l_BK_PS_cr_i_pt_Win"}, {"actionName": "Flash_Back_To_Restore_Point"}, {"actionName": "Execute_Sq_l_Script_With_Password"}, {"actionName": "Execute_Sq_l_Script_With_En_v_Password"}, {"actionName": "Execute_Redo_Ctr_l_BK_PW_in_D_S"}, {"actionName": "Execute_Redo_Ctr_l_BK_PW_in"}, {"actionName": "Execute_DB_Command"}, {"actionName": "Drop_Restore_Point"}, {"actionName": "Database_Switch_Log_D_S"}, {"actionName": "Create_Temp_File_Table_Space"}, {"actionName": "Create_Temp_File"}, {"actionName": "Create_Standby_Control_File"}, {"actionName": "Create_Restore_Point"}, {"actionName": "Create_Control_File_Script"}, {"actionName": "Create_Control_File_By_Script"}, {"actionName": "Copy_Redo_Ctr_File"}, {"actionName": "Compare_Table_Count_PR"}, {"actionName": "Compare_Table_Count_DR"}, {"actionName": "Check_No_Logging_Operation"}, {"actionName": "Check_Flash_Back_On"}, {"actionName": "Back_Up_Control_File"}, {"actionName": "Alter_Database_Open"}, {"actionName": "Apply_Incremental_Logs"}, {"actionName": "Alter_System_Set_Log_Archive_De_st_Enable"}, {"actionName": "Alter_System_Set_Log_Archive_De_st_Defer"}, {"actionName": "Alter_System_Log"}, {"actionName": "Alter_St_by_DB_to_Max_Performance"}, {"actionName": "Alter_Database_Mount"}, {"actionName": "Alter_Database_Flash_Back_On"}, {"actionName": "Alter_Database_Flash_Back_Off"}, {"actionName": "Alter_Database_Convert_Physical_St_by"}, {"actionName": "Alter_Database_Active_St_by"}, {"actionName": "Alter_DB_Recover_Managed_St_by_DB_Cancel"}, {"actionName": "Active_Database_Read_Write"}]}]}]}], "Network": [{"categoryName": "Network", "workflowCategoryBaseChildViewListVms": [{"name": "F5 Load Balancer", "workflowCategoryChildViewListVms": [{"name": "F5 LTM", "actionLists": [{"actionName": "Execute_Force_Enable_Node_State_Of_Virtual_Server"}, {"actionName": "Execute_Force_Disable_Node_State_Of_Virtual_Server"}, {"actionName": "Execute_Enable_LT_MV_i_rt_u_al_Server"}, {"actionName": "Execute_Disable_LT_MV_i_rt_u_al_Server"}, {"actionName": "Check_LT_MV_i_rt_u_al_Server_State"}]}, {"name": "F5 Cloud Service", "actionLists": [{"actionName": "Check_F_5_<PERSON>ad_Balancer_Default_Route_Pools_Name_Exist"}, {"actionName": "Check_F_5_Load_Balancer_Name_Exist_and_Enabled"}, {"actionName": "Check_API_Token_Expiry_Date"}, {"actionName": "Execute_Migrate_F_5_Load_Balancer_Default_Route_Pools_Name_Change"}]}]}, {"name": "NS1_DNS", "workflowCategoryChildViewListVms": [{"name": "NS1_DNS", "actionLists": [{"actionName": "Modify_NS_1_DNS_Records_A_Type_Multiple_IPAddress"}, {"actionName": "Modify_NS_1_DNS_Record_C_NAME_Type"}, {"actionName": "Modify_NS_1_DNS_Record_A_Type_Single_IPAddress"}, {"actionName": "Check_NS_1_DNS_Record_C_NAME_Type"}, {"actionName": "Check_NS_1_DNS_Record_A_Type_Single_IPAddress"}, {"actionName": "Check_NS_1_DNS_Record_A_Type_Multiple_IPAddress"}]}]}, {"name": "InfoBlox", "workflowCategoryChildViewListVms": [{"name": "InfoBlox", "actionLists": [{"actionName": "INFO_BL_OX_Query_DNS"}, {"actionName": "INFO_BL_OX_Modify_DNS"}, {"actionName": "INFO_BL_OX_Delete_DNS"}, {"actionName": "INFO_BL_OX_DNS_Query_Statistics_View"}, {"actionName": "INFO_BL_OX_Add_DNS"}, {"actionName": "Delete_C_Name_Record"}, {"actionName": "Execute_Enable_Disable_DNS_Record_mapping"}, {"actionName": "Check_DNS_Record_Status"}, {"actionName": "Check_If_DNS_Record_Exist_In_View_and_Zone"}, {"actionName": "Check_if_D_SN_Zone_Exist_In_DNS_view"}, {"actionName": "Check_DNS_View_Exist"}, {"actionName": "Modify_C_Name_Record"}, {"actionName": "Add_C_Name_Record"}]}]}, {"name": "Routers", "workflowCategoryChildViewListVms": [{"name": "Routers", "actionLists": [{"actionName": "Check_V_la_n"}, {"actionName": "Check_Static_Route"}]}]}, {"name": "FireWall", "workflowCategoryChildViewListVms": [{"name": "FireWall", "actionLists": [{"actionName": "Update_Firewall_Policy"}]}]}, {"name": "<PERSON><PERSON><PERSON>", "workflowCategoryChildViewListVms": [{"name": "OpenShift", "actionLists": [{"actionName": "Open_Shift_Monitoring"}, {"actionName": "Execute_Scale_Up_Down_Pods_Deployments_Count"}, {"actionName": "Check_Pod_Deployments_Count_with_All_Ready_Status"}, {"actionName": "Execute_Scale_Up_Down_Pods_Replica_Sets_Count"}, {"actionName": "Check_Pod_Replica_Sets_Count_with_All_Ready_Status"}, {"actionName": "Execute_Scale_Up_Down_Pods_State_f_u_l_Sets_Count"}, {"actionName": "Check_Pod_State_f_u_l_Sets_Count_with_All_Ready_Status"}, {"actionName": "Execute_Scale_Up_Down_Machine_Sets_Machines_Count"}, {"actionName": "Check_Open_Shift_Machine_Set_Machine_Count"}, {"actionName": "Execute_Virtual_Machine_Stop"}, {"actionName": "Execute_Virtual_Machine_Start"}, {"actionName": "Check_Specific_Virtual_Machine_Status"}, {"actionName": "check_pod_dep_ploy_men_t_test"}]}]}, {"name": "Switches", "workflowCategoryChildViewListVms": [{"name": "Switches", "actionLists": [{"actionName": "Update_Air_Gap_Action_I_M"}, {"actionName": "Enable_Port_Action_I_M"}, {"actionName": "Verify_Port_Enable_Action_I_M"}, {"actionName": "Verify_Port_Disable_Action_I_M"}, {"actionName": "Disable_Port_Action_I_M"}, {"actionName": "Nexus_Add_Route"}]}]}]}], "storage": [{"categoryName": "Storage", "workflowCategoryBaseChildViewListVms": [{"name": "Common", "workflowCategoryChildViewListVms": [{"name": "Common", "actionLists": [{"actionName": "Unmount_Volume"}, {"actionName": "Mount_Volume"}]}]}, {"name": "NetApp", "workflowCategoryChildViewListVms": [{"name": "SnapMirror", "actionLists": [{"actionName": "Verify_Lu_n_s_Status"}, {"actionName": "Snap_Mirror_Volume_Restrict"}, {"actionName": "Snap_Mirror_Volume_Online"}, {"actionName": "Snap_Mirror_Update"}, {"actionName": "Snap_Mirror_Res_y_n_c"}, {"actionName": "Snap_Mirror_Check_Vol_Status"}, {"actionName": "Snap_Mirror_Break"}, {"actionName": "Initial_is_e_Snap_Mirror"}, {"actionName": "Verify_Snap_Mirror_Status"}, {"actionName": "Net_App_Snap_Mirror_Monitor"}]}]}, {"name": "EMC", "workflowCategoryChildViewListVms": [{"name": "Data Domain", "actionLists": [{"actionName": "Sync_Replication_and_Verify"}, {"actionName": "Sync_Replication"}, {"actionName": "Delete_Mt_re_e"}, {"actionName": "Create_Mt_re_e_Replication_Pair"}, {"actionName": "Create_Mt_re_e"}, {"actionName": "Check_mt_re_e_Replication_Pair_Status"}]}, {"name": "STAR", "actionLists": [{"actionName": "Verify_Protect_Status"}, {"actionName": "U_n_protect_Site"}, {"actionName": "Switch_Site"}, {"actionName": "Split_Status"}, {"actionName": "Protect_Site"}, {"actionName": "Isolate"}, {"actionName": "Halt_CG"}, {"actionName": "Enable_Star"}, {"actionName": "Disconnect_Site"}, {"actionName": "Disable_Star"}, {"actionName": "Connect_Site"}]}, {"name": "AVAMAR", "actionLists": [{"actionName": "PPD_MB_ac_k_up"}, {"actionName": "Cross_v_Center_VM_Migration"}, {"actionName": "Check_v_Center_Exist_Ont_he_PPD_MS_er_v_er_Asset_Source"}, {"actionName": "Restore_VM_To_Alternate_Location_Multiple_New"}, {"actionName": "VM_Instant_Access_Restore"}, {"actionName": "Restore_VM_To_Alternate_Location_Multiple_New"}, {"actionName": "Restore_VM_To_Alternate_Location_Multiple"}, {"actionName": "Restore_VM_To_Alternate_Location_Proxy"}, {"actionName": "Restore_VM_To_Alternate_Location_New"}, {"actionName": "Restore_VM_To_Alternate_Location"}, {"actionName": "Restore_Virtual_Machine_To_New_Location"}, {"actionName": "Restore_Virtual_Machine_To_Original_Location"}, {"actionName": "Replicating_All_Backups_For_Specific_Client"}, {"actionName": "Replicating_All_Backups_For_Specific_Domain"}, {"actionName": "Restore_Virtual_Machine_From_History_To_New_Location"}, {"actionName": "Restore_Virtual_Machine_Frm_History_To_Original_Lo_ca"}, {"actionName": "Check_if_Domain_Exist_After_Replication"}, {"actionName": "Replicate_Backup"}, {"actionName": "Check_if_Client_Exists"}, {"actionName": "Check_if_Client_Exist_After_Replication"}, {"actionName": "Check_Virtual_Machine_Latest_Backup_Image_Exist"}, {"actionName": "Chk_Virtual_Machine_Latest_Backup_Exist_Aft_r_Replica"}, {"actionName": "Check_VM_Exist_And_Protect_New"}, {"actionName": "Check_VM_Exist_And_Protect"}, {"actionName": "Check_Replication_Activity_Status"}, {"actionName": "Chk_Lats_t_Baku_p_I_mg_Ex_st_Of_Tr_gt_VM_For_Rs_tr_Op_rt_n"}, {"actionName": "Check_If_Target_VM_Exist_For_Restore"}, {"actionName": "Check_If_Target_VC_enter_Name_Exist_For_Restore"}, {"actionName": "Check_If_Target_Exist_For_Restore_Operation"}, {"actionName": "Check_If_Domains_Name_Exist"}, {"actionName": "Add_v_Center_To_PPD_MA_ss_et_Source"}, {"actionName": "Add_Credentials_To_PPD_MT_y_pe_VC_ENTER"}]}, {"name": "CyberVaultRecovery", "actionLists": [{"actionName": "VM_Instant_Access_Restore_Multiple"}, {"actionName": "Execute_Sync_Copy"}, {"actionName": "Execute_Secure_Copy_Analyze"}, {"actionName": "Execute_Secure_Copy"}, {"actionName": "Execute_Recovery_Check"}, {"actionName": "Execute_Policy_Application_Recovery"}, {"actionName": "Execute_Copy_Analyze"}, {"actionName": "Check_Latest_Copy_Available_For_Pert_i_cu_lar_Policy"}, {"actionName": "Check_Last_Analysis_Status"}, {"actionName": "Check_Recovery_Latest_Selected_Check_Status"}]}, {"name": "DMX SRDF", "actionLists": [{"actionName": "EM_CS_WITCH_OVER_DG"}, {"actionName": "EM_CS_WITCH_BACK_DG"}, {"actionName": "EM_CS_WA_PP_ERS_ON_ALI_TY"}, {"actionName": "EM_CS_PL_IT_DEVICE_GROUP"}, {"actionName": "EM_CS_ETAS_Y_NC_MODE"}, {"actionName": "EM_CS_ETA_CPD_E_VICE_MODE"}, {"actionName": "EM_CR_ES_UM_EDE_VICE_GROUP"}, {"actionName": "EM_CI_SS_PL_IT"}, {"actionName": "EM_CI_SD_GR_ESE_NT"}, {"actionName": "EM_CI_SD_G_CONSISTENT"}, {"actionName": "EM_CF_AIL_OVER_DEVICE_GROUP"}, {"actionName": "EM_CESTA_BL_IS_HD_E_VICE_GROUP"}, {"actionName": "EM_CEN_ABLE_DEVICE_GROUP"}, {"actionName": "EM_CD_IS_ABLE_DEVICE_GROUP"}, {"actionName": "EM_CC_HECK_DG_TRACKS_ZERO"}]}, {"name": "RECOVER POINT", "actionLists": [{"actionName": "Disable_Group_Image_Access"}, {"actionName": "Fail_over_Protection"}, {"actionName": "Enable_Group_Image_Access"}, {"actionName": "Fail_over_Protection_Groups_et"}, {"actionName": "Verify_Logged_Access"}, {"actionName": "Verify_Group_Statistics"}, {"actionName": "Verify_Group_State"}, {"actionName": "Verify_Direct_Access"}, {"actionName": "Verify_Data_Transfer"}, {"actionName": "Start_Group_Data_Transfer"}, {"actionName": "Set_Production_Copy"}, {"actionName": "Pause_Group_Data_Transfer"}, {"actionName": "Is_Group_Sync"}]}, {"name": "EMCISILON", "actionLists": [{"actionName": "Sync_Policy_Allow_Writes"}, {"actionName": "Run_Sync_Job"}, {"actionName": "Modify_Sync_Policy_Schedule"}, {"actionName": "Fail_over_Sync_Policy"}]}]}, {"name": "IBM", "workflowCategoryChildViewListVms": [{"name": "DISK Global Mirror", "actionLists": [{"actionName": "Disk_Global_Mirror_Monitor"}, {"actionName": "Update_Target_Storage_Group"}, {"actionName": "Switch_Over"}, {"actionName": "Perform_Data_Synchronization"}, {"actionName": "Pause_Global_Mirror"}, {"actionName": "Is_CG_Formed"}, {"actionName": "Switch_Back"}, {"actionName": "Resume_Global_Mirror"}, {"actionName": "Execute_Storage_Command"}, {"actionName": "Execute_D_SW_ait_For_No_Flash_Copy"}, {"actionName": "Execute_D_SP_a_use_GM_IR"}, {"actionName": "Execute_D_SM_a_k_e_Flash"}, {"actionName": "Execute_D_SM_a_k_e_Session"}, {"actionName": "Execute_D_SM_a_k_e_GM_IR"}, {"actionName": "Execute_D_SM_K_Flash"}, {"actionName": "Execute_D_SF_ail_Over_PPR_C"}, {"actionName": "Execute_D_SF_ail_Back_PPR_C"}, {"actionName": "Execute_D_SC_om_mi_t_Flash"}, {"actionName": "Execute_D_SC_heck_No_Flash_Copy"}, {"actionName": "Execute_D_SC_heck_Flash_Zero_Tracks"}, {"actionName": "Execute_D_SC_hang_e_Session_Remove"}, {"actionName": "Execute_D_SC_hang_e_Session_Add"}, {"actionName": "Execute_D_SC_h_Vol_Group_Remove"}, {"actionName": "Execute_D_SC_h_Vol_Gr_p_Add"}, {"actionName": "Execute_D_SC_om_man_d"}, {"actionName": "Execute_Check_D_SC_om_man_d"}, {"actionName": "Execute_D_SW_ait_For_Flash_Tracks_Zero"}, {"actionName": "Execute_D_SS_et_Flash_Revertible"}, {"actionName": "Execute_D_SR_evert_Flash"}, {"actionName": "Execute_D_SR_ever_s_e_Flash"}, {"actionName": "Execute_D_SR_es_y_n_c_Flash"}, {"actionName": "Execute_D_SR_emo_v_e_GM_IR"}, {"actionName": "Execute_D_SRM_Flash"}, {"actionName": "Execute_D_SP_a_use_PPR_C"}, {"actionName": "Check_Storage_Mailbox_Path"}, {"actionName": "Check_DST_racks_LS_PPR_C"}, {"actionName": "Execute_D_SR_es_um_e_PPR_C"}, {"actionName": "Execute_D_SR_es_um_e_GM_IR"}]}]}, {"name": "HDS", "workflowCategoryChildViewListVms": [{"name": "HUR", "actionLists": [{"actionName": "Hitachi_Pair_Swap"}, {"actionName": "Hitachi_Pair_display_DR"}, {"actionName": "Hitachi_Pair_display_PR"}, {"actionName": "Swap_Application_Node_Replication"}, {"actionName": "Resume_Application_Node_Replication"}, {"actionName": "Pause_Application_Node_Replication"}, {"actionName": "Check_the_Replication_Paused_State"}, {"actionName": "Check_the_Replication_State_OK"}, {"actionName": "Check_the_Replication_Swapped_State"}, {"actionName": "H_UR_Monitor"}, {"actionName": "Hitachi_Pairs_pl_it"}, {"actionName": "Hitachi_Pair_Re_Sync"}, {"actionName": "Hitachi_Is_Vol_Suspended"}, {"actionName": "Hitachi_Hor_cc_takeover"}]}]}, {"name": "HP3PAR", "workflowCategoryChildViewListVms": [{"name": "HP3PAR", "actionLists": [{"actionName": "HP_3_PARS_tor_age_Monitoring"}, {"actionName": "Stop_RC_op_y_Group"}, {"actionName": "Set_RC_op_y_Group_Restore"}, {"actionName": "Set_RC_op_y_Group_Fail_Over"}, {"actionName": "Execute_Sync_RC_op_y"}, {"actionName": "Check_RC_op_y_Group_Sync_Status"}]}]}, {"name": "HUAWEI", "workflowCategoryChildViewListVms": [{"name": "HUAWEI", "actionLists": [{"actionName": "Sync_Replication_H_u_awe_i"}, {"actionName": "Swap_Replica_Role"}, {"actionName": "Split_Replication"}, {"actionName": "Enable_Secondary_Resource_Access_Read_Write"}, {"actionName": "Enable_Secondary_Resource_Access_Read_Only"}, {"actionName": "Check_Secondary_Resource_Access_Read_Write"}, {"actionName": "Check_Secondary_Resource_Access_Read_Only"}, {"actionName": "Check_Replication_Running_Status"}, {"actionName": "Check_LUN_Split_State"}, {"actionName": "Check_Consistency_Group_Time_Value"}, {"actionName": "Chk_Consist_n_c_y_Group_Status_Synchronization_B_g_n_s"}, {"actionName": "Check_Consistency_Group_Replica_Role"}, {"actionName": "Change_Consistency_Group_Time_Value"}]}]}, {"name": "Pure Storage", "workflowCategoryChildViewListVms": [{"name": "Pod Promotion", "actionLists": [{"actionName": "Promote_Pod"}, {"actionName": "Demote_Pod"}, {"actionName": "Replication_Data_lag"}, {"actionName": "Pod_Replication_Direction"}, {"actionName": "Pod_Promotion_Status"}, {"actionName": "Pod_Replication_Status"}]}]}]}, {"categoryName": "File System", "workflowCategoryBaseChildViewListVms": [{"name": "VxVm", "workflowCategoryChildViewListVms": [{"name": "VxVm", "actionLists": [{"actionName": "U_n_Mount_F_S"}, {"actionName": "Stop_V_x_VM_DG"}, {"actionName": "Start_V_x_VM_DG"}, {"actionName": "Mount_F_S"}, {"actionName": "Is_V_x_VM_DG_Volume_Enable"}, {"actionName": "Is_V_x_VM_DG_Volume_Disable"}, {"actionName": "Is_F_S_U_n_Mounted"}, {"actionName": "Is_F_SM_o_u_n_ted"}, {"actionName": "Import_V_x_VM_DG"}, {"actionName": "Deport_V_x_VM_DG"}, {"actionName": "Check_DG_All_Vol_Enabled"}, {"actionName": "Check_DG_All_Vol_Disabled"}]}]}, {"name": "FileHandling", "workflowCategoryChildViewListVms": [{"name": "FileActions", "actionLists": [{"actionName": "Rename_Folder"}, {"actionName": "Rename_File"}, {"actionName": "Copy_Folder"}, {"actionName": "Copy_File"}]}]}]}, {"categoryName": "Replication", "workflowCategoryBaseChildViewListVms": [{"name": "eBDR", "workflowCategoryChildViewListVms": [{"name": "eBDR", "actionLists": [{"actionName": "Stop"}, {"actionName": "Start"}, {"actionName": "Resume"}, {"actionName": "Pause"}, {"actionName": "Canc_l_e"}]}]}, {"name": "GoldenGate", "workflowCategoryChildViewListVms": [{"name": "GoldenGate", "actionLists": [{"actionName": "Stop_Golden_Gate_Group"}, {"actionName": "Start_Golden_Gate_Group"}, {"actionName": "Check_Golden_Gate_Group_Status_Is_Running"}, {"actionName": "Check_Golden_Gate_Group_RB_AS_y_n_c"}, {"actionName": "Golden_Gate_Replication"}]}]}, {"name": "RSync", "workflowCategoryChildViewListVms": [{"name": "RSync", "actionLists": [{"actionName": "Stop_Rs_y_n_c_Replication"}, {"actionName": "Execute_Rs_y_n_c_Job"}, {"actionName": "Verify_Sync_Status"}, {"actionName": "Verify_Connectivity"}, {"actionName": "RS_y_n_c_Replication"}, {"actionName": "Rs_y_n_c_App_Monitor"}, {"actionName": "RS_y_n_c_App_Replication"}]}]}, {"name": "RoboCopy", "workflowCategoryChildViewListVms": [{"name": "RoboCopy", "actionLists": [{"actionName": "Rob_o_copy_Monitoring"}, {"actionName": "Rob_o_copy_Replication"}]}]}, {"name": "DoubleTake", "workflowCategoryChildViewListVms": [{"name": "DoubleTake", "actionLists": [{"actionName": "Execute_Fail_Over"}, {"actionName": "Verify_Fail_Over"}, {"actionName": "Check_Status_Execute_Start"}, {"actionName": "Verify_Job_Start"}, {"actionName": "Execute_Fail_back"}, {"actionName": "Verify_Fail_Back"}, {"actionName": "Execute_Restore_Job"}, {"actionName": "Verify_Job_Restore_Status"}, {"actionName": "Verify_Job_Status"}, {"actionName": "Veri<PERSON>_<PERSON>_Reverse"}, {"actionName": "Execute_Reverse"}, {"actionName": "D_TV_er_if_y_Replication_Status"}, {"actionName": "D_TV_er_if_y_Replication_Queue"}, {"actionName": "D_TV_er_if_y_Job_Name"}, {"actionName": "DTS_top_Job"}, {"actionName": "DTS_tart_Job"}, {"actionName": "D_<PERSON>_Job_Restore"}, {"actionName": "D_T_Job_Perform_Fail_Over"}, {"actionName": "D_T_Job_Perform_Fail_Back"}, {"actionName": "D_T_Job_Check_Not_Is_In_Error"}, {"actionName": "D_T_Job_Check_Mirror_State"}, {"actionName": "D_T_Job_Check_Mirror_Perm_ill_age"}, {"actionName": "D_T_Job_Check_Mirror_Bytes_Remaining"}, {"actionName": "D_T_Job_Check_Is_In_Error"}, {"actionName": "D_T_Job_Check_High_Level_State"}, {"actionName": "D_T_Job_Check_Health"}, {"actionName": "D_T_Job_Check_Disk_Queue_Bytes"}, {"actionName": "D_T_Job_Check_Can_Stop"}, {"actionName": "D_T_Job_Check_Can_Start"}, {"actionName": "D_T_Job_Check_Can_Restore"}, {"actionName": "D_T_Job_Check_Can_Fail_Over"}, {"actionName": "D_T_Job_Check_Can_Fail_Back"}, {"actionName": "Check_Status_Execute_Reverse"}]}]}]}, {"categoryName": "Veritas Cluster", "workflowCategoryBaseChildViewListVms": [{"name": "Cluster", "workflowCategoryChildViewListVms": [{"name": "Cluster", "actionLists": [{"actionName": "Execute_VC_SR_es_our_c_e_ONLINE"}, {"actionName": "Execute_VC_SR_es_our_c_e_OFFLINE"}, {"actionName": "Check_VC_SR_es_our_c_e_State"}, {"actionName": "VV_RC_heck_Service_Group_State_Of_Cluster_Node"}, {"actionName": "VV_RC_heck_Service_Group_Node_Showing_Offline"}, {"actionName": "VV_RC_heck_Service_Group_Cluster_Node_Showing_Online"}, {"actionName": "VV_RS_G_Check_If_String_Value_Exist_In_File"}, {"actionName": "VV_RS_GE_x_ecu_t_e_Batch_File"}, {"actionName": "VV_RS_GE_x_ecu_t_e_ONLINE_Any_One_Of_Server_Group"}, {"actionName": "VV_RS_GE_x_ecu_t_e_OFFLINE_Any_One_Of_Server_Group"}, {"actionName": "VV_RS_G_Check_Status_Of_Service_Group_Cluster_Name"}, {"actionName": "VV_RC_heck_Link_State_Up_To_Date_Status"}, {"actionName": "VV_RC_heck_Link_State_In_Replication_Status"}, {"actionName": "VV_RS_witch_Node_State_In_Cluster"}, {"actionName": "VV_R_Get_Service_Group_State_On_Specified_Node"}]}]}]}], "APP": [{"categoryName": "Mailing System", "workflowCategoryBaseChildViewListVms": [{"name": "MsExchangeServer", "workflowCategoryChildViewListVms": [{"name": "DAG2016", "actionLists": [{"actionName": "Vrfy_Ts_t_R_pl_ct_ion_Health_Pas_d_Stats_Exc_pt_DB_Av_lb_l_t_y"}, {"actionName": "Verify_Server_Component_Active_State_fora_ll_Cm_p_ne_n_t"}, {"actionName": "Verify_Replay_Queue_Status_for_All_Mailbox_DB"}, {"actionName": "Verify_Primary_Active_Manager_State_at_PR"}, {"actionName": "Verify_Mailbox_DB_Mounted_Status_at_PRS_it_e_Level"}, {"actionName": "Verify_Mailbox_DB_Mounted_Status_at_PRS_er_v_er_Level"}, {"actionName": "Verify_Mailbox_DB_Healthy_Status_at_DR_Site_Level"}, {"actionName": "Verify_Mailbox_DB_Healthy_Status_at_DR_Server_Level"}, {"actionName": "<PERSON><PERSON><PERSON>_DB_Copy_Act_vat_n_Pol_c_y_Set_in_g_fr_Ma_lb_ox_Ser_v_r"}, {"actionName": "Vrfy_Content_Index_State_as_Healthy_fora_ll_Mailbox_DB"}, {"actionName": "Stop_DAG_at_Primary_Site_Without_Configuration_Only"}, {"actionName": "Stop_DAG_at_Primary_Site_With_Configuration_Only"}, {"actionName": "Start_DAG"}, {"actionName": "Set_DB_Copy_Auto_Activation_Policy"}, {"actionName": "Send_e_Receive_Connector"}, {"actionName": "Restore_DAG"}, {"actionName": "Redirect_Mailbox_Server_Ms_g_Queue_to_Other_Server"}, {"actionName": "Move_Primary_Active_Manager_to_Target"}, {"actionName": "Move_Database_Without_Skip"}, {"actionName": "Move_Database_With_Skip"}, {"actionName": "Move_Active_Mailbox_Database_with_Skip_Options"}, {"actionName": "Mount_Data_Base"}, {"actionName": "Exch_DAG_Verify_Witness_Server_And_Path_Configured"}, {"actionName": "Exch_DAG_Verify_Alt_r_n_t_e_Wit_ne_s_Server_Ad_Directory"}, {"actionName": "Exch_DAGS_et_Witness_Server_And_Directory"}, {"actionName": "Exch_DAGS_et_Alternate_Witness_Server_And_Directory"}, {"actionName": "Enable_Send_Connector"}, {"actionName": "Enable_Mailbox_Database_Circular_Logging"}, {"actionName": "Dismount_Database"}, {"actionName": "Disable_Send_Connector"}, {"actionName": "Disable_Mailbox_Database_Circular_Logging"}, {"actionName": "Check_Transport_Sever_Queue_Status"}, {"actionName": "Check_Site_Level_Mailbox_All_Server_Status_under_DAG"}, {"actionName": "Chk_Mailbox_database_Backup_Full_Incremental_Status"}, {"actionName": "Check_Mailbox_Server_Message_Queue_Count"}, {"actionName": "Check_Mailbox_Database_Copy_Status"}, {"actionName": "Check_Database_Copy_Auto_Activation_Policy"}]}, {"name": "DAG", "actionLists": [{"actionName": "Check_All_Mailbox_DB_Mounted_Status"}, {"actionName": "Verify_Passive_MB_X_Replay_Queue_Length"}, {"actionName": "Verify_Passive_MB_X_DB_Copy_Status"}, {"actionName": "Verify_Passive_MB_X_DB_Copy_Error_Message"}, {"actionName": "Verify_Passive_MB_X_DB_Copy_Content"}, {"actionName": "Verify_Passive_MB_X_Copy_Queue_Length"}, {"actionName": "Verify_DAGS_tat_us_Online"}, {"actionName": "Verify_Active_MB_X_DB_Copy_Status"}, {"actionName": "Verify_Active_MB_X_DB_Copy_Error_Message"}, {"actionName": "Verify_Active_MB_X_DB_Copy_Content"}, {"actionName": "Suspend_When_Ready_To_Complete_Mode_Status"}, {"actionName": "Suspend_When_Ready_To_Complete_Mode"}, {"actionName": "Stopped_Mail_Box_Servers"}, {"actionName": "Started_Mail_Box_Servers"}, {"actionName": "Resume_Move_Mail_Box_Req"}, {"actionName": "Remove_User_Mail_Box_Move_Req_If_Exist"}, {"actionName": "Move_Active_Mailbox_Database"}, {"actionName": "Get_Move_Req_Status"}, {"actionName": "Get_Move_Req_Statics"}, {"actionName": "Check_User_Move_Req_Can_Be_Created"}, {"actionName": "Check_User_Mail_Box_Exist"}]}, {"name": "SCR", "actionLists": [{"actionName": "Set_SCR_Prerequisite"}, {"actionName": "SCR_Status_With_Copy_Queue_Length"}, {"actionName": "Resume_SCR"}, {"actionName": "Move_Mailbox_Configuration"}, {"actionName": "Mount_Database"}, {"actionName": "Get_Mailbox_List_Count_Before_Switch"}, {"actionName": "Get_Mailbox_List_Count"}, {"actionName": "Enable_SCR"}, {"actionName": "Dismount_Mailbox_database"}, {"actionName": "Disable_SCR_Restore_logs"}, {"actionName": "Create_Storage_Group"}, {"actionName": "Compare_New_SG_Mailbox_Path"}, {"actionName": "Check_target_DB_Status"}, {"actionName": "Check_Target_DB_File_Status"}, {"actionName": "Allow_File_Restore_To_Mailbox_DB"}]}, {"name": "DAGFO", "actionLists": [{"actionName": "Verify_DAG_Parameter_Value"}, {"actionName": "Stop_DAGO_n_Mail_Box_Server"}, {"actionName": "Stop_DAG_Active_Dir_Site_Conf_i_g_Only"}, {"actionName": "Start_DAGO_n_Mail_Box_Server"}, {"actionName": "Set_Preferred_ADS_er_v_er"}, {"actionName": "Set_Database_Availability_Group"}, {"actionName": "Restore_DAG_Active_Dir_Site"}, {"actionName": "Move_Cluster_Group"}, {"actionName": "Move_Active_Mail_Box_Database"}, {"actionName": "Check_Mail_Box_DB_Status_Parameter_Value"}]}, {"name": "DAG2010", "actionLists": [{"actionName": "Verify_Replication_Service_on_PAM"}, {"actionName": "Verify_Mailbox_Server_Entries"}, {"actionName": "Stop_Database_Availability_Group_On_Mailbox_Server"}, {"actionName": "Stop_Database_Availability_Group"}, {"actionName": "Set_Public_Folder_Database"}, {"actionName": "Set_Database_Copy_Auto_Activation_Policy_Blocked"}, {"actionName": "Set_Database_Copy_Auto_Activation_Policy"}, {"actionName": "Restore_Database_Availability_Group"}, {"actionName": "Move_Mailbox_Database_To_DR_Server"}, {"actionName": "Move_Default_offline_Address_Book"}, {"actionName": "Execute_Power_Shell_Command"}, {"actionName": "Exch_DAG_Verify_Started_d_Mail_Box_Server"}, {"actionName": "Verify_Cur_n_t_Site_Name_Associated_Wth_Mail_bx_Ser_v_r"}, {"actionName": "Exch_DAG_Verify_Check_and_Stop_cluster_Service"}, {"actionName": "Exch_DAGS_top_DAG_At_PR_Mailbox_Server"}, {"actionName": "Exch_DAGS_tart_DAG_At_PR_Mailbox_Server"}, {"actionName": "Exch_DAGS_et_DAG_To_Seed_ALL_Changes"}, {"actionName": "Resume_Mail_Box_Database_Copy"}, {"actionName": "Mount_Mailbox_Database_Active_PR_Mailbox_Server"}, {"actionName": "Exch_DAG_Check_Replication_Health_on_Mailbox_Server"}, {"actionName": "Check_Primary_Alternate_File_Share_Witness_In_Use"}, {"actionName": "Check_Replication_Health_Status"}, {"actionName": "Check_Mailbox_Server_Role_Services_Status"}, {"actionName": "Check_Healthy_Mailbox_Database_Details"}, {"actionName": "Check_Database_Mounted_Status"}, {"actionName": "Check_DAG_Membership_Status"}]}]}]}, {"categoryName": "Web", "workflowCategoryBaseChildViewListVms": [{"name": "IIS", "workflowCategoryChildViewListVms": [{"name": "IIS", "actionLists": [{"actionName": "Stop_II_SW_e_b_site"}, {"actionName": "Stop_Application_Pool"}, {"actionName": "Start_II_SW_e_b_site"}, {"actionName": "Start_Application_Pool"}]}]}, {"name": "HTTP", "workflowCategoryChildViewListVms": [{"name": "HTTP", "actionLists": [{"actionName": "Hit_HTTP_Ur_l"}, {"actionName": "Hit_Check_HTTP_Ur_l"}, {"actionName": "Execute_Check_Rest_API_Command"}]}]}]}, {"categoryName": "Workflow", "workflowCategoryBaseChildViewListVms": [{"name": "CP", "workflowCategoryChildViewListVms": [{"name": "WorkFlow", "actionLists": [{"actionName": "No_Relic_at_ion"}, {"actionName": "DR_Ready"}]}]}, {"name": "WorkFlow", "workflowCategoryChildViewListVms": [{"name": "WORKFLOW", "actionLists": [{"actionName": "DR_Operation_Type"}, {"actionName": "Wait_For_Workflow_Action"}, {"actionName": "Wait_For_Parallel_Action"}, {"actionName": "Display_Alert"}, {"actionName": "Stop_Workflow_RT_O"}, {"actionName": "Start_Workflow_RT_O"}]}]}]}, {"categoryName": "Cloud", "workflowCategoryBaseChildViewListVms": [{"name": "DELL", "workflowCategoryChildViewListVms": [{"name": "<PERSON><PERSON>", "actionLists": [{"actionName": "C_y_be_r_Recovery_Execute_Copy_Analyze"}, {"actionName": "C_y_be_r_Recovery_Execute_Secure_Copy"}, {"actionName": "PPR_Execute_Recovery_Application_PPD_M"}, {"actionName": "CR_Chk_if_latest_copy_Available_particular_pol_c_y"}, {"actionName": "C_y_be_r_Recovery_Check_Status"}, {"actionName": "C_y_be_r_Recovery_Execute_Secure_Copy_Analyze"}]}, {"name": "PPDM", "actionLists": [{"actionName": "PPD_ME_x_ecu_t_e_Protected_Single_VM"}, {"actionName": "PPD_MV_MC_op_y_Restore_To_Alter_net_Location_V_er_1"}, {"actionName": "PPD_MC_heck_if_MSS_QL_DB_Backup_copy_exist_V_er_12"}, {"actionName": "PPD_M_1_Check_VM_exist_protected_V_er_4"}, {"actionName": "PPD_ME_x_ecu_t_e_Multiple_U_n_Protected_VM_Backup"}, {"actionName": "PPD_ME_x_ecu_t_e_Single_U_n_Protected_VM_Backup"}, {"actionName": "PPD_MR_stor_VM_to_Alt_r_n_t_lo_cwt_lats_t_Backup_copy"}, {"actionName": "PPD_MR_str_MSS_QL_DB_TO_ALT_RNA_TE_Ml_t_pl_DB_to_D_f_ult_V_er_11"}, {"actionName": "DELL_EM_CP_PD_MM_SS_QL_BR_est_or_Multiple_DB_V_er_11"}, {"actionName": "PPD_ME_x_ecu_t_e_Protected_Multiple_VM"}]}]}, {"name": "AzureLoadBalancer", "workflowCategoryChildViewListVms": [{"name": "AzureLoadBalancer", "actionLists": [{"actionName": "Remove_Probe_Health_from_Load_Balancer"}, {"actionName": "Remove_Load_Balancer_Rule_From_Load_Balancer"}, {"actionName": "Rm_v_e_Lo_dB_ala_n_cr_In_b_oud_NAT_Rule_From_Load_Balancer"}, {"actionName": "Remove_Load_Balancer_From_VM"}, {"actionName": "Add_Probe_Health_To_Load_Balancer"}, {"actionName": "Add_<PERSON><PERSON>_Balancer_To_VM"}, {"actionName": "Add_Load_Balancer_Rule_To_Load_Balancer"}, {"actionName": "Add_Load_Balancer_Inbound_NAT_Rule_To_Load_Balancer"}]}]}, {"name": "AzureToHyperV", "workflowCategoryChildViewListVms": [{"name": "AzureToHyperV", "actionLists": [{"actionName": "Execute_Reverse_Replication_Azure_To_On_Prim"}, {"actionName": "Execute_Repro_t_e_ct_On_Prim_To_Azure"}, {"actionName": "Execute_Repro_t_e_ct_On_Prem_VM_ware_To_Azure"}, {"actionName": "Execute_Repro_t_e_ct_Azure_To_On_Prim"}, {"actionName": "Execute_Planned_Fail_over_Commit_On_Prim_To_Azure"}, {"actionName": "Execute_Planned_Fail_over_Azure_To_On_Prim"}, {"actionName": "Execute_Commit_Fail_over"}, {"actionName": "Check_Replicated_VM_Protection_State"}, {"actionName": "Check_Allowed_Operation_Azure_To_Prim"}]}]}, {"name": "TCLCloud", "workflowCategoryChildViewListVms": [{"name": "TCLCloud", "actionLists": [{"actionName": "TC_LC_loud_VM_Instance_Power_ON"}, {"actionName": "TC_LC_loud_VM_Instance_Power_OFF"}, {"actionName": "Check_TC_LC_loud_VM_Instance_Status"}]}]}, {"name": "Rackware", "workflowCategoryChildViewListVms": [{"name": "Rackware", "actionLists": [{"actionName": "Rack_ware_Start_Wave_in_RM_MS_er_v_er"}, {"actionName": "Rack_ware_Execute_Wave_Fail_Over"}, {"actionName": "Rack_ware_Execute_Wave_Fail_Back"}, {"actionName": "Rack_ware_Check_Wave_Status"}, {"actionName": "Rack_ware_Check_DR_Policy_Status"}, {"actionName": "Host_Sync_When_Target_Exist_Without_Wait"}, {"actionName": "Host_Sync_When_Target_Exist_With_Wait"}, {"actionName": "Cr_t_e_W_av_Wt_H_st_Set_OC_IA_ut_op_r_o_vs_on_para_mt_rs"}, {"actionName": "Create_Host_Sync_Auto_Tr_gt_n_t_Av_l_Static_IP_Wth_out_Wait"}, {"actionName": "Create_Host_Sync_Auto_Target_n_t_Av_l_Stat_c_IP_Wth_Wait"}, {"actionName": "Create_Host_Sync_Auto_Target_n_t_Av_l_Dy_n_m_c_IP_Without_Wt"}]}]}, {"name": "OracleCloud", "workflowCategoryChildViewListVms": [{"name": "OracleCloud", "actionLists": [{"actionName": "Oracle_Cloud_VM_Instance_Action"}, {"actionName": "De_attach_ALL_NS_Gs_from_Instance_V_NI_C"}, {"actionName": "Create_NS_Gand_Add_Security_Rule"}, {"actionName": "Check_if_NS_GA_t_tache_d_to_Instance_V_NI_C"}, {"actionName": "Check_Oracle_Cloud_VM_Instance_Status"}, {"actionName": "Add_New_NS_Gt_o_Instance_V_NI_CR_e_place_Existing_NS_Gs"}, {"actionName": "Add_New_NS_Gt_o_Instance_V_NI_CA_pp_end_to_Existing_NS_Gs"}]}]}, {"name": "SoftLayer", "workflowCategoryChildViewListVms": [{"name": "SoftLayer", "actionLists": [{"actionName": "Soft_Layer_Upgrade_Virtual_Machine_Memory"}, {"actionName": "Soft_Layer_Upgrade_Virtual_Machine_CPU"}, {"actionName": "Soft_Layer_Upgrade_Virtual_Machine_By_Ids"}, {"actionName": "Soft_Layer_Upgrade_Virtual_Guest"}, {"actionName": "Soft_Layer_Provision_Virtual_Machine_By_Ids"}, {"actionName": "Soft_Layer_Provision_Virtual_Guest"}, {"actionName": "Soft_Layer_Power_ON_Virtual_Guest"}, {"actionName": "Soft_Layer_Power_OFF_Virtual_Guest"}, {"actionName": "Soft_Layer_Check_Virtual_Guest_Power_ON"}, {"actionName": "Soft_Layer_Check_Virtual_Guest_Power_OFF"}]}]}, {"name": "AzureApplicationGateway", "workflowCategoryChildViewListVms": [{"name": "AzureApplicationGateway", "actionLists": [{"actionName": "Remove_Routing_Rule"}, {"actionName": "Rm_v_e_Path_b_sd_Ru_l_fr_m_B_ck_end_Pool_Ru_l"}, {"actionName": "Ch_ck_Listener_with_Associated_Rule"}, {"actionName": "Chk_App_li_ct_ion_Gat_w_y_Ru_l_Path_exist"}, {"actionName": "Chk_App_l_cation_Gat_w_y_Op_rat_n_l_State"}, {"actionName": "Add_Routing_Rule"}, {"actionName": "Add_Listener_HTTP_Type"}, {"actionName": "Ad_App_l_tn_Gt_e_w_y_B_ck_end_Pool_Ru_l_Path"}]}]}, {"name": "OracleCloud_DNS", "workflowCategoryChildViewListVms": [{"name": "OracleCloud_DNS", "actionLists": [{"actionName": "OC_ID_el_et_e_DNS_Record_A_Type"}, {"actionName": "OC_IC_heck_if_DNS_Record_Exist_A_Type"}, {"actionName": "OC_IC_heck_if_DNS_Record_Does_Not_Exist_A_Type"}, {"actionName": "OC_IC_heck_Existing_TTL_Value_DNS_Record_A_Type"}, {"actionName": "OC_IA_dd_DNS_Record_A_Type"}]}]}, {"name": "G42", "workflowCategoryChildViewListVms": [{"name": "G42", "actionLists": []}]}, {"name": "Azure", "workflowCategoryChildViewListVms": [{"name": "AzureMysql", "actionLists": [{"actionName": "Azure_My_SQL_Execute_Service_Stop_Start"}, {"actionName": "Azure_My_SQL_Check_Service_Start_Stop_Status"}, {"actionName": "Execute_Promote_My_SQL_Replica_Server"}, {"actionName": "Execute_Create_My_SQL_Replica_Server"}, {"actionName": "Execute_Delete_My_SQL_Standalone_Server"}, {"actionName": "Execute_Delete_My_SQL_Replication_Replica_Server"}, {"actionName": "Azure_Mys_ql_Monitoring"}, {"actionName": "Execute_Delete_My_SQL_Replication_Source_Server"}, {"actionName": "Check_My_SQL_Server_Available_Status"}, {"actionName": "Check_Azure_My_SQL_Server_Role"}, {"actionName": "Check_Azure_My_SQL_Server_Exist_int_he_Region"}]}, {"name": "AzurePostgreSQL", "actionLists": [{"actionName": "Azure_Post_gr_e_SQL_Execute_Service_Stop_Start"}, {"actionName": "Azure_Post_gr_e_SQL_Check_Service_Stop_Start"}, {"actionName": "Azure_Post_gr_e_Sq_l_Monitoring"}]}, {"name": "AzureVMStatus", "actionLists": [{"actionName": "St_p_V_rt_u_l_M_ch_ne_Scale_All_Instances"}, {"actionName": "St_p_Sp_cf_i_c_V_rt_u_l_M_ch_n_Sc_l_St_Inst_n_c_e"}, {"actionName": "Stop_My_Sq_l_Replication"}, {"actionName": "Str_t_V_rt_u_l_M_ch_ne_Scale_Al_Instances"}, {"actionName": "Start_Sp_cf_c_V_rt_u_l_M_ch_n_Sc_l_e_St_Inst_n"}, {"actionName": "Remove_Security_Rule_to_NS_G"}, {"actionName": "Execute_Unplanned_Fail_over"}, {"actionName": "Exc_t_U_n_plan_d_Fail_O_v_r_On_Pr_m_To_Azure"}, {"actionName": "Execute_Re_Protect"}, {"actionName": "Execute_Planned_Fail_over"}, {"actionName": "Execute_Planned_Fail_Over_Azure"}, {"actionName": "Execute_Force_Fail_Over_Azure"}, {"actionName": "Execute_Fail_over_Azure_Cosmos_DB"}, {"actionName": "Execute_Commit_Unplanned_Fail_over"}, {"actionName": "Enable_Disable_ATM_Endpoint"}, {"actionName": "Dissociate_NS_Gt_o_VM"}, {"actionName": "Dissociate_NS_Gt_o_Net_w_r_k_Interface"}, {"actionName": "Delete_Replication_Source_Server"}, {"actionName": "Delete_Replication_Replica_Server"}, {"actionName": "Create_Replication_AZ_My_Sq_l"}, {"actionName": "Chk_V_rt_u_al_Machine_Scale_Set_Status"}, {"actionName": "Chk_U_n_plan_d_Fail_o_v_r_Comp_ltd_Status"}, {"actionName": "Chk_U_n_plan_d_Fail_o_v_r_Com_it_ed_Status"}, {"actionName": "Ck_Sp_cf_c_V_rt_u_l_M_ch_ne_Sc_l_e_Set_Status"}, {"actionName": "Check_Re_Protect_Protection_State"}, {"actionName": "Check_Re_Protect"}, {"actionName": "Check_Public_IPAddress"}, {"actionName": "Check_Planned_Fail_over_Status"}, {"actionName": "Check_NS_G_Name_Exist"}, {"actionName": "Check_NS_G_Name_Associate_to_VM"}, {"actionName": "Check_Enable_Disable_ATM_Endpoint"}, {"actionName": "Check_Commit_Fail_Over"}, {"actionName": "Check_Azure_Write_Location_Cosmos"}, {"actionName": "Check_Azure_SQL_DB_Role"}, {"actionName": "Check_Azure_Replication_State"}, {"actionName": "Check_Azure_Read_Location_Cosmos"}, {"actionName": "Chk_A_z_DB_Act_Prov_s_n_in_g_Stats_Cosmos"}, {"actionName": "Check_Allowed_Operation"}, {"actionName": "Chk_AZ_My_SQL_Sr_v_r_Available_Status"}, {"actionName": "Check_AZ_My_SQL_Role"}, {"actionName": "Azure_Test_Fail_Over"}, {"actionName": "Azure_Clean_Up_Fail_Over"}, {"actionName": "Associate_Public_IPAddress"}, {"actionName": "Associate_NS_Gt_o_VM_Sp_cf_y_N_t_w_r_k_Card"}, {"actionName": "Associate_NS_Gt_o_VM_D_fa_l_t_N_t_w_r_k_Card"}, {"actionName": "Associate_NS_Gt_o_Network_Interface"}, {"actionName": "As_ign_NS_Gt_o_VM_Wth_R_place_Ex_st_n_g_NS_G"}, {"actionName": "Add_Security_Rule_to_NS_G"}, {"actionName": "VC_D_Verify_Status"}, {"actionName": "VC_DP_owe_r_On"}, {"actionName": "VC_DP_owe_r_Off"}, {"actionName": "Stop_Azure_VM"}, {"actionName": "Start_Azure_VM"}, {"actionName": "Check_Azure_VM_Status"}, {"actionName": "Check_Azure_VM_Power_State"}, {"actionName": "Change_Azure_VM_Size"}]}, {"name": "AzureMssql", "actionLists": [{"actionName": "Azure_MSS_QL_Pass_Monitoring"}, {"actionName": "Execute_SQL_DB_Forced_Fail_over"}, {"actionName": "Execute_SQL_DB_Planned_Fail_over"}, {"actionName": "Check_AZ_Replication_State"}, {"actionName": "Check_AZ_SQL_DB_Pa_a_SR_ole"}]}, {"name": "AzureStorage", "actionLists": [{"actionName": "Check_Storage_Replication_Data_Lag_With_Input"}, {"actionName": "Set_Storage_Account_Replication"}, {"actionName": "Execute_Storage_Account_Fail_over"}, {"actionName": "Check_Storage_Account_Type"}, {"actionName": "Azures_tor_age_replication_Monitoring"}]}, {"name": "AzureSiteRecovery", "actionLists": [{"actionName": "Execute_Commit_Fail_over_AS_R"}, {"actionName": "Execute_U_n_Planned_Fail_over_AS_R"}, {"actionName": "Execute_Repro_t_e_ct_AS_R"}, {"actionName": "Execute_Test_Fail_over_Clean_Up_AS_R"}, {"actionName": "Check_AS_RT_est_Fail_over_Status"}, {"actionName": "Execute_Test_Fail_over"}, {"actionName": "Check_AS_RT_est_Fail_over_Cleanup_Status"}, {"actionName": "Check_Unplanned_Fail_over_Status"}, {"actionName": "Check_Commit_Fail_over_Status"}, {"actionName": "Check_AS_R_Re_Protect_Status"}, {"actionName": "Check_AS_RV_MP_rote_ct_ion_Status"}]}]}, {"name": "<PERSON><PERSON><PERSON>", "workflowCategoryChildViewListVms": [{"name": "<PERSON><PERSON><PERSON>", "actionLists": [{"actionName": "Rub_r_i_k_Mount_Virtual_Machine"}, {"actionName": "Migrate_Data_Store_After_Mount"}]}]}, {"name": "Amazon", "workflowCategoryChildViewListVms": [{"name": "EC2", "actionLists": [{"actionName": "Stop_EC_2_Instance"}, {"actionName": "Start_EC_2_Instance"}, {"actionName": "Modify_EC_2_Instance_Size"}, {"actionName": "Is_EC_2_Instance_UP"}, {"actionName": "Is_EC_2_Instance_Down"}, {"actionName": "Check_EC_2_Instance_State"}]}, {"name": "S_3", "actionLists": [{"actionName": "Upload_To_S_3_Bucket"}, {"actionName": "Upload_Files_To_S_3_Bucket"}, {"actionName": "Download_From_S_3_Bucket"}, {"actionName": "Download_Files_From_S_3_Bucket"}]}]}]}, {"categoryName": "Cyber Recovery", "workflowCategoryBaseChildViewListVms": [{"name": "DELL", "workflowCategoryChildViewListVms": [{"name": "<PERSON><PERSON>", "actionLists": [{"actionName": "C_y_be_r_Recovery_Execute_Copy_Analyze"}, {"actionName": "C_y_be_r_Recovery_Execute_Secure_Copy"}, {"actionName": "PPR_Execute_Recovery_Application_PPD_M"}, {"actionName": "CR_Chk_if_latest_copy_Available_particular_pol_c_y"}, {"actionName": "C_y_be_r_Recovery_Check_Status"}, {"actionName": "C_y_be_r_Recovery_Execute_Secure_Copy_Analyze"}]}, {"name": "PPDM", "actionLists": [{"actionName": "PPD_ME_x_ecu_t_e_Protected_Single_VM"}, {"actionName": "PPD_MV_MC_op_y_Restore_To_Alter_net_Location_V_er_1"}, {"actionName": "PPD_MC_heck_if_MSS_QL_DB_Backup_copy_exist_V_er_12"}, {"actionName": "PPD_M_1_Check_VM_exist_protected_V_er_4"}, {"actionName": "PPD_ME_x_ecu_t_e_Multiple_U_n_Protected_VM_Backup"}, {"actionName": "PPD_ME_x_ecu_t_e_Single_U_n_Protected_VM_Backup"}, {"actionName": "PPD_MR_stor_VM_to_Alt_r_n_t_lo_cwt_lats_t_Backup_copy"}, {"actionName": "PPD_MR_str_MSS_QL_DB_TO_ALT_RNA_TE_Ml_t_pl_DB_to_D_f_ult_V_er_11"}, {"actionName": "DELL_EM_CP_PD_MM_SS_QL_BR_est_or_Multiple_DB_V_er_11"}, {"actionName": "PPD_ME_x_ecu_t_e_Protected_Multiple_VM"}]}]}]}, {"categoryName": "Third Party", "workflowCategoryBaseChildViewListVms": [{"name": "<PERSON><PERSON><PERSON>", "workflowCategoryChildViewListVms": [{"name": "<PERSON><PERSON><PERSON>", "actionLists": [{"actionName": "VB_VM_Ware_Replica_Fail_Over_Planned"}, {"actionName": "VB_RV_M_ware_Replica_Undo_Fail_Over"}, {"actionName": "VB_RV_MW_are_Replica_Undo_Fail_back"}, {"actionName": "VB_RV_MW_are_Replica_Fail_Over_Permanent"}, {"actionName": "VB_RV_MW_are_Replica_Fail_Over_DR_Only"}, {"actionName": "VB_RV_MW_are_Replica_FAIL_BACK_To_Original_VM"}, {"actionName": "Fail_Over_Replication_Job"}, {"actionName": "Fail_Back_Replication_Job"}, {"actionName": "Fail_Back_Commit_Replication_Job"}, {"actionName": "Create_Replication_Job_Use_Prod_VM_State"}, {"actionName": "Create_Replication_Job_From_Backup_Files"}, {"actionName": "Check_Replication_Job_Last_State"}, {"actionName": "Check_Replication_Job_Last_Result"}, {"actionName": "Check_Replication_Job"}, {"actionName": "Check_Replica_Monitoring_Status"}]}]}, {"name": "eBDRPerpetuuiti", "workflowCategoryChildViewListVms": [{"name": "eBDRPerpetuuiti", "actionLists": [{"actionName": "e_BD_R_Operations"}]}]}]}, {"categoryName": "System Management Tools", "workflowCategoryBaseChildViewListVms": [{"name": "Oracle_Ops_Center", "workflowCategoryChildViewListVms": [{"name": "Oracle_Ops_Center", "actionLists": [{"actionName": "Update_An_Asset"}, {"actionName": "Unman_age_<PERSON>_<PERSON><PERSON>"}, {"actionName": "Remove_Asset_In_Maintenance_Mode"}, {"actionName": "Refresh_An_Asset"}, {"actionName": "Reboot_Asset"}, {"actionName": "Put_Asset_In_Maintenance_Mode"}, {"actionName": "Power_On_Asset"}, {"actionName": "Power_Off_Asset"}, {"actionName": "Manage_An_Asset"}, {"actionName": "Execute_Plan"}, {"actionName": "Execute_Ops_Center_Command"}, {"actionName": "Execute_Check_Ops_Center_Command"}]}]}]}, {"categoryName": "Server Management", "workflowCategoryBaseChildViewListVms": [{"name": "HP1", "workflowCategoryChildViewListVms": [{"name": "HP1", "actionLists": [{"actionName": "HP_Power_ON_Blade_Server"}, {"actionName": "HP_Power_OFF_Blade_Server"}, {"actionName": "HP_Check_Power_Status_On"}, {"actionName": "HP_Check_Power_Status_Off"}]}]}, {"name": "Sun ILOM", "workflowCategoryChildViewListVms": [{"name": "Sun ILOM", "actionLists": [{"actionName": "Power_On_Off_Status"}, {"actionName": "Power_On"}, {"actionName": "Power_Off"}]}]}, {"name": "Base24", "workflowCategoryChildViewListVms": [{"name": "Base24", "actionLists": [{"actionName": "MSS_QL_links_tat_us"}, {"actionName": "Execute_T_x_n_Que_Check"}, {"actionName": "Execute_Status_Node_Check"}, {"actionName": "Execute_SA_F_COUNT_CHECK"}, {"actionName": "Execute_DR_Net_Check_Status_State"}, {"actionName": "Execute_DR_Net_Check_Records"}, {"actionName": "Exc_ut_e_DR_Net_Check_Distributor_File_Map_Status_Queue"}, {"actionName": "Execute_DR_Net_Check_Collector_File_Map_Mode"}, {"actionName": "Execute_DR_Net_Check_Audit_Mode_Status"}, {"actionName": "Execute_Base_24_Command"}, {"actionName": "Execute_Base_24_Check_Command"}]}]}]}]}]