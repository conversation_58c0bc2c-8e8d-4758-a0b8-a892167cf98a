Did you know Python has been around for more
than 30 years
and yet it is one of the most popular
programming languages in the world.
So it is no wonder that most companies,
even man companies,
ask questions on Python in their
interviews.
And if you are someone who is looking for a
job as a data analyst, data scientist,
or even as a developer, then
this video is for you.
In this video, we'll cover top 21 Python
conceptual questions
that are often asked in Python interviews.
This video is part of our new series on
interview preparation,
where we'll bring to you
valuable material on programming, data
science and generative AI.
So do subscribe to our channel
so that you get free access
to all of our learning material that is
coming to you as part of the series.
On this note,
let's begin our discussion on the top 21
Python interview questions.
All right, our first question is why is
Python called an interpreted language?
Interpreted language means any programming
language which executes instructions
directly
and line by line.
It means it does not compile the program
like C or Java does.
Rather, it directly runs the complete
program
all at once.
And while running the program, wherever it
encounters an error, it stops.
And if you think about it, this makes
Python easier to debug.
Because you get to know exactly where the
error occurred
in your code.
Now Python doesn't need
a separate compilation step like C or Java.
You can just write Python code and run it
directly.
Here's what it means.
C or Java is required
to be built and linked.
Building process involves using a compiler
to translate the human related source code
into machine readable object code files.
The linking process
combines these object code files
together into a final executable program.
These steps are necessary in languages like
C or Java
to produce an executable program
that a computer can
thereafter run.
On the other hand, in Python you can
directly run the code without going into
any of these separate steps.
You can just write Python code and run it
directly.
This is the precise reason why
Python is fast to develop as well.
Now let's move on to our next question.
Do we need indentation in Python?
The obvious answer is yes.
You need indentation because it is part of
the language's syntax.
Now let's understand why.
Indentation helps in reading the code
better,
and that is why Python has made it
compulsory.
Every programming language provides
mechanism to manage and control how the
coding blocks are written.
In Python it is indentation
and it is different in Java.
For example, in Java when
we need to construct a for loop,
we have to put the blocks or statements
inside the for loop within the braces
and the statements inside the for loop need
not be indented.
It can be in the same line.
But when it comes to Python, the print
statement inside the for loop should have
spaces, that is 4 spaces.
That is how indentation works in Python.
All right, now let's move on to the third
question.
What are built-in data types in Python?
In Python, a data type defines the kind of
values a piece of data can have
and what operations can be performed on it.
Let's understand in this way.
Imagine different kinds of containers,
a jar, a jug, a
box, and a basket.
Now, each container is suitable for holding
different kinds of things.
You wouldn't put water in a basket, right?
It's for things like fruits.
Similarly, in Python you use different data
types to hold different kinds of values
like integers or strings.
Now let us look at various data types we
have in Python.
We have text type,
we have numeric types, sequence types, map
types, set types, boolean type, binary
types, and none type.
And in Python, in case you want to know the
data type of a variable,
just use the type function.
Let's see how it works.
Now in output, you will see the data type
of the variable.
The type function basically takes the
variable as an argument and gives the data
type of the variable.
All right, let's move on to our 4th
question.
What are classes and objects in Python?
In Python,
a class is a blueprint for creating
objects.
On the other hand, an object is an instance
of a class.
What do I mean by this?
Well, class can be considered to be your
blueprint, and objects are real world
entities which are
defined or created from classes.
Let's take an example here.
This is a blueprint of a house.
The blueprint defines the layout of the
house, the number of rooms, the
type of rooms including kitchen, bathroom,
bedroom, etc,
the material used, etc.
And now with this blueprint we can create
an unlimited number of houses.
An object, on the other hand, is an
instance of a class.
It's a real tangible thing that you create
using the blueprint.
Over here it is House 1, House 2 and House 3
are in fact objects, actual houses
created from the House class, which is the
blueprint.
They have the structure defined by the
House class.
Now let's move on to our fifth question,
what is a dictionary in Python?
Dictionary is an unordered collection of
elements.
In other words, a dictionary in Python is a
built in data structure for storing
collections of objects or values.
These elements in a dictionary are stored
as key value pairs.
This means there is a specific key
and there is a value which is assigned to a key.
This is pretty much like a real
life dictionary where
you look up a word
known as a key
to get its definition on a specific page
which is known as the value.
For example, in the dictionary, my
dictionary over here, the key is name
and the value
for the key is John.
In this example,
name, age, city are the keys
and John 30 and New York are their
respective values.
This dictionary represents a person
and we can use it to look up details about
the person like their name, age and city.
By the way, dictionaries are mutable in
nature.
You can modify the dictionary by adding,
changing, or removing key value pairs,
as shown in this example.
All right, let's move on to question number six.
What are Python functions
and how do they help in code optimization?
A function is a block of code
that can be called by other parts of your
program.
In other words, it's a piece of reusable
code that you can run whenever you need it.
Instead of writing the same code multiple times.
So how do you write a function?
First, you define functions using the def
keyword followed by the function name,
parenthesis and a colon followed by it.
The code block within every function is
indented
and this is how a function looks like.
In this example, addnumbers is a function
that takes 2 parameters,
#1 and #2,
adds them together and returns the result.
When we call add numbers three
five,
it executes the code within the function
and returns the sum of three and five,
which is 8.
So how does it help in code optimization?
Well,
functions allow you to reuse code by
encapsulating it in a single place
and calling it multiple times from
different parts of your program.
In this example, say hello is a function
that doesn't take any parameters.
When we call say hello,
it simply prints hello world.
And what else? Well, functions give
improved readability to your program.
By dividing your code into logical blocks,
functions can make your code more readable
and easier to understand.
It also reduces redundancy of your code
and makes it cleaner and readable.
Functions also make code easier for
testing.
Functions allow you to test individual
blocks of code
separately,
so you can debug individual functions
independently of the rest of the code.
It also provides improved performance.
Functions can also help to improve the
performance of your code by allowing you to
use optimized code libraries
or by allowing the
Python interpreter to optimize the code
more effectively.
Now let's move on to question #7.
What are Python sets?
Explain some of the properties of sets.
In Python, a set is an unordered collection
of unique objects.
Sets are defined using curly braces.
Each and every value is separated by
commas.
And you know what is more interesting? It
is not ordered.
For example,
we give input
apple, banana,
cherry and while giving output the order is
changed.
So as you can see it is not ordered.
Similarly, sets are
often used to store a collection of
distinct objects.
That means they automatically remove
duplicate values.
For example, over here
we have defined a set
having
elements apple, banana, cherry, orange
and if we add another element to this set
in the output you will see apple is not
repeated twice.
Now sets are also used to perform
membership tests, that is to check if an
object is indeed present in a set
and this is how it is done.
And for this piece of code the output is yes
because banana.
Is
definitely present in the
fruit set.
Now let's move on to the properties of sets.
First one is sets are unordered as I
explained.
Secondly, sets are unique.
Third is sets are mutable, so you can add
or remove elements from a set using the add
and remove methods.
And fourthly, sets are not indexed.
Sets do not
support indexing or slicing,
so you cannot access individual elements.
Of a set using an index.
All right, now let's move on to question #8.
What is the difference between list and a
tuple? List and tuple are essentially data
structures.
So let's first understand what data
structures are in Python.
Data structures are a way of storing and
organizing data efficiently.
Why is this important?
When you store your data in an unorganized
manner,
this will allow you to
easily access and perform operations on the data.
Now you will want to store data in
different ways to cater to the need of the hour.
Maybe you want to store all data types
together, or you want to store data in
such a manner which will help in faster
retrieval, or maybe in some ways that
stores only distinct data items.
Luckily, Python has a host of inbuilt data
structures.
Out of all,
list and tuple
are two types of built in data structures
that are used to store collections of data.
Now let's look at the differences.
First is mutability.
Lists are mutable.
They can be modified after creation, so you
can do adding, removing, changing items, etc.
Whereas tuples are immutable.
Once they are created,
their content cannot be changed.
Second difference is syntax.
Lists are defined using square brackets as
you can see,
and tuples are defined using parentheses.
Again you can see
third key difference is performance.
Lists take up more memory due to their
ability to change and grow, whereas tuples
are more memory efficient
and usually faster because they are
immutable.
And this is how a list looks like.
And this is how a tuple looks like.
Alright,
now let's move on to our question #9.
What is the difference between a module and
a package? Now, this is a very common
question asked during interviews.
Modules and packages in Python
are responsible for implementing the
modular programming paradigm.
First,
let's see what a module is.
Module in Python is simply a dot PY file
containing Python code.
It can contain any number of functions,
classes and variables.
It is a very common practice to put
together
a set of related functionalities together
so that you can reuse them whenever you want.
Now how do you do that?
Once a module is created, it can be
imported and used in another Python script
using the import statement.
Once you use this syntax,
this will import all the functions, classes
and variables that are present inside that
module.
Let's see how a module is used.
First, we'll create a file called math dot PY.
This will contain functions of arithmetic
operations, that is addition,
then multiplication, subtraction and
division.
Then we create another file
called arithmetic dot PY
where we import the maths
dot PY file at the very beginning.
We do that by writing import maths as you
can see.
Now let's see how it works.
We assign a value of two to X.
And three to Y
and thereafter we call
add and multiply functions from the math
module. And
as you can see the output
we get is what is expected
5:00 and 6:00.
Now what if I want to add more complex
functionalities? For that I need to create
another file
and import it as another module.
But just imagine how repetitive it would be
if we have to import 10 such modules.
Now there is a solution for that.
We put together all the related modules
inside a directory
and we call it a package.
So a package in simple terms is a
collection of modules.
It is a way of organizing related Python
modules into a directory.
Basically, it's a
directory that contains multiple module
files
along with a standard file called
init.py, which tells Python that the
directory is indeed a package.
Now let's move on to our question #10.
What is indexing
and what is negative indexing? We need to
understand the concept of iterables first
before we get into indexing.
It is a special type of object in Python
that can
iterate over.
What do I mean by that?
Well, you can cross or go through all the
different elements or entities contained
within the
object,
and the object could be a list, a tuple, a
string, dictionary, etc.
Are all iterable in Python and yes, the
iteration part could easily be achieved
with the help of.
For loops.
So in simple words, iterables are something
you can loop over.
Now when we understand what iterables are,
now let's focus on indexing.
Indexing is basically a way to access
individual elements or groups of
elements
in an iterable
by their position.
In other words, you can access a specific
element of your choice within an iterable,
such as a list,
and do your operations with it depending on
your needs.
Let me show you how I have this list of
strings with various fruit names.
In the very beginning, Python allocates
memory for the
list and each element is indexed this way.
In Python, indexing starts from zero as you
can see.
So if you have a list called mylist
having elements
like apple, banana, cherry,
the index of apple is 0,
banana is 1 and cherry is 2.
And this indexing that we have discussed is
the one which.
Is generally used and is called positive
indexing.
Now what is negative indexing?
Negative indexing simply means the
last element of the collection has an index of
one
and the second last
has 2 and so on.
Now let's move on to question #11, which is
explain the logical operators in Python.
In Python, the logical operators AND, OR and NOT
can be used to perform boolean operations.
This is done in order to determine whether
a condition is true or false.
What do I mean by this? Well, in Python
there can be situations where there are two
or more conditions and we need to check
which condition is true and which is false.
And for these situations we use logical
operations.
There are three types of logical operators,
AND, OR and NOT.
The AND operator returns true if both
operands
are true.
If either condition is false, it returns false
like this.
Then or operator returns true if at least
one of the operand is true.
If both conditions are false, it returns false,
right? Thereafter,
not operator returns the opposite of
operand.
If the condition is true, it returns false,
and if the condition is false, it returns true.
Logical operators are often used in control
flow constructs such as if,
elif and while statements to combine
conditions
and make decisions based on multiple
criteria.
Now let's move on to our question #12.
Explain the use of Lambda expression in Python
and when is it useful?
Well, the Lambda expression or function is
used to define a function without a name in
Python.
It is also known as anonymous function
because it doesn't have a name
like regular functions do.
The difference is that in a regular
function you have to define the function.
Lambda is useful in situation where you
need a small temporary function that you won't
need to use elsewhere in your code.
And this is how Lambda function looks.
Lambda function can have any number of
arguments,
but can contain only a single expression.
This means you have to write the entire
thing in one line.
One of the foremost use cases of Lambda is
in functional programming,
wherein it allows you to supply a function
as a parameter
to a different function, for example map,
filter, etc.
Now let's move on to question #13.
Explain slicing in Python.
Slicing in Python is a feature that allows
you to access a subset or a slice of a list
or a sequence.
In other words, slicing a list
gives you another list instead of a single
element.
This means
each object in the list has a specific index.
Slice specifies a start index and an
end index of a list.
For example, in the list apple has an index of
0, banana has an index of one, cherry has
index of two, and so on.
Now I want the fruit, banana, cherry and
date from the list.
We would need to slice it out from the list.
So we slice it out over here.
By the way, keep in mind that the sublist
returned will contain the element starting
from the start index, but will exclude the
end index element.
So we see the output is basically banana,
cherry and date.
One more thing,
if you leave out the start index, it will
assume it to be 0,
and if you leave out the end index again,
the output will be the entire length of the list.
All right, now let's move on to question #14.
Explain
the difference between mutable and
immutable objects in Python.
For this, let's first understand what an
object is in Python.
In Python,
almost everything is an object, including
numbers, strings, lists, dictionaries,
functions,
and even modules.
Now, objects can be of two types, mutable
and immutable.
If you remember correctly, we have already
talked about this.
When we were talking about list being
mutable and tuple being immutable.
Now let me talk about this in detail.
So what does mutable means?
These are objects whose content or state
can be changed after they are created.
We have seen an example of a list
which is a mutable object before.
We have seen an example of a list which is
a mutable object.
Now
let's see a different example with a
dictionary.
In this example we have a dictionary called
student.
And we modify the value associated with the
key age from 25 to 26.
We also add a new key value pair gender
female to the dictionary.
Because the dictionary is mutable, so its
content can be changed and updated as
needed.
Moving on, what are immutable objects?
Well, these are objects whose content or
state cannot be changed after they are
created.
If you try to change the state, you will
actually create a new object.
We have seen how a tuple looks like.
Another example of a mutable object is a
string.
As you can see in this code,
trying to directly change the character at
index zero in the string name
will raise a type error because strings are
immutable.
Instead, we need to create a new string
modified name
with the desired change, which is replacing
the first character with M.
Now let's move on to question #15.
What is the use of pass keyword in Python?
So pass is a null statement that basically
does nothing.
It is often used as a placeholder
where a statement is required syntactically
but no action needs to be taken.
So basically it's just there to ensure your
code runs
without syntax errors until you are ready
to write the real code for that block.
Let me show you how.
In this case, pass allows you to define the
function myfunction
without any actual code.
This can be useful when you are sketching
out a program and aren't ready to write the
function yet.
Now let's move on to question #16.
What do generators
tell about their use in Python? Suppose you
want to make a huge list of numbers from 1
to a billion.
If you create a list for this, it will
consume a lot of your computer's memory,
which is
not efficient at all.
Instead, what you can do is you can create
a generator that produces these numbers one
at a time and only when they are needed.
This way you
only need memory for one number at a time,
not all billion numbers together.
Well, the official definition goes
something like this.
A generator in Python returns an iterator
that produces sequences of values one at a time.
We use the yield keyword to produce values.
Now let's see how it works.
In this example,
countupto is a generator that counts from
one up to
the value of north.
The yield keyword is used to specify what
value the generator should produce when
it's iterated over.
Then it pauses the function until the next
value is needed.
The function state, including local
variable, is saved between yields
so it can pick up where it left off.
When you use the generator in a for loop
with count up to let's say 10,
it prints the number one to 10 one at a time.
It generates each number as the for loop
asks for the next number rather than
generating all the numbers at once, which
would be less memory efficient.
Moving on to question 17,
what is shallow copy and deep copy in
Python? Well, in Python, deep copy and
shallow copy are methods used to copy
objects.
When we use the equal operator, it
only creates a new variable that shares the
reference of the original output.
In order to create real copies or clones of
these objects,
we can use the copy module in Python.
They are of two types, shallow copy and
deep copy.
Now let's focus on shallow copy first.
A shallow copy is
a copy of an object that stores the
reference of the
original elements.
The simple way to understand this is
it's like making a list of items you own.
If any of those items is a box with more
stuff inside,
your list just mentions the box, not the
stuff inside.
So if you change the content of the box, it
will be changed in both the original and
the copy.
Now let's come to deep copy.
A deep copy is a process where we create a
new object
and copy elements recursively.
The independent copy is created of the
original object
and its entire set of elements.
Changes made in one object do not affect
the other.
What do I mean by this?
It's like making a detailed list of
items you own.
If you have a box with more stuff inside of it,
you list down all the stuff in the box too.
So even if you change the contents of the
box in the original list, it won't change
in the copy.
Let's move on to question #18.
What is inheritance in Python? Also, what
is the difference between single and
multiple inheritance?
Well, in Python, inheritance is a way to
define a new class, also called a child class.
That takes on attributes and methods from
an existing parent class.
What do I mean by this?
Imagine a family where a child inherits
traits from their parents, like their hair color
or height.
In Python, inheritance works similarly.
A child class can inherit characteristics
and behaviors from a parent class.
Now there are two types of inheritance,
single inheritance
and multiple inheritance.
Single inheritance is when a class inherits
from a single superclass or a parent class.
Think of it as a child who inherits
traits from one parent.
Let me show you how.
In this code,
child is inheriting from the parent.
It means
child class has
all the methods that parent has, plus any
additional methods
as defined.
Now let's see what multiple inheritance is.
Multiple inheritance is when
a class can inherit from more than one
superclass or parent class.
In our analogy, imagine a child who can
inherit traits from both parents.
In this case, child is inheriting both from
the mother and the father,
so it
has
access to the methods from both classes.
The order of inheritance matters in
multiple inheritance.
So if both mother and father had a method
with the same name,
the method from mother would be used
because mother appears first.
Now let's move on to question 19.
What is exception handling and how is it
done in Python?
Well, exception handling in Python involves
using special blocks of code
so that it can catch and handle errors or
exceptions that occur when your program runs.
Basically, it is done to help your program
keep running even when something unexpected
happens.
The main keywords
for exception handling in Python are try,
except, finally, and raise.
At this point, let me show you how it works
in this code.
If you enter a valid integer, it prints the
number and
this gets executed no matter what.
Whereas if you enter something that's not a
valid integer like a string or a float,
it raises a value error.
The exception that moves to the except block
prints that's not a valid number
and then this gets executed no matter what.
The finally block is optional and is used
for code that you want to be executed
no matter whether an exception occurred or not.
Now let's move on to question #20.
What is the use of decorators in Python?
In Python, decorators are a special kind of
function
that add extra functionality to another
function.
What do I mean by this?
Well, they provide a way to wrap a function
with another function
which can add functionality
before or after the original function is called
without permanently modifying it.
Let me show you how.
When you call say hello,
it's not directly called anymore, it's now
wrapped with the wrapper function inside my
decorator.
The
mydecorator syntax is just an easier way of
saying say hello
equal to mydecorator
say hello.
Now let's look at the output.
In this example, mydecorator is a decorator
that takes a function as input
and defines a new function wrapper
that adds some code before and after
calling function.
The
at the rate mydecorator line in Python's
decorator syntax for applying the decorator
to the say hello function.
Alright, now we come to our last question.
Which is explain the difference between is
and the equality operator in Python.
The equality operator checks for value
equality.
It compares the values of the two objects
and returns true if they are equal
and false if they are not equal.
It is basically a comparison operator that
checks for equality.
It compares the value of the two objects to
determine if they are equal.
In this case, list one and list two are two
different objects.
But they have the same value.
Therefore list one
is
equal to list 2 is actually true. Now is
checks for identity.
It returns true if both variables point to
the same object and false otherwise.
Basically it checks whether the two
variables point to the same object in
memory,
not their content.
In this case, list one and list two are
pointing to the same object in memory.
So list one is list 2 is true.
So guys, that's all we had for this video
for you.
Hope you liked that content.
If you did, give it a thumbs up
and do subscribe to our channel for more
interesting data tech content.
Good luck to you. Bye.
