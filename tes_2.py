import torch
from langchain.document_loaders import <PERSON><PERSON><PERSON>oader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.vectorstores import Chroma
import json
from langchain.docstore.document import Document

# Check for CUDA availability
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# Method 1: Using standard JSON loading
with open(r".\w_test_json.json", 'r', encoding='utf-8') as file:
    data = json.load(file)

# Process the JSON data into text format
processed_texts = []
for item in data:
    if isinstance(item, dict):
        # Combine fields into a single text string, including age
        name = item.get('name', '')
        age = item.get('age', '')
        secret_identity = item.get('secretIdentity', '')
        powers = ", ".join(item.get('powers', []))  # Join powers list into a string
        # Create a text block for each character
        text_content = f"Name: {name}, Age: {age}, Secret Identity: {secret_identity}, Powers: {powers}"
        processed_texts.append(text_content)

# Create Document objects from processed texts
documents = [Document(page_content=text) for text in processed_texts]

# Split the documents into chunks
text_splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=200)
text_chunks = text_splitter.split_documents(documents)

# Load HuggingFace embeddings
embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")

# Embed the text chunks and store them in a vector store
vectorstore = Chroma.from_documents(text_chunks, embeddings)

# View embeddings of the first chunk (if available)
if text_chunks:
    print("First text chunk:")
    print(text_chunks[0].page_content)
    print("\nEmbedding for the first chunk:")
    print(embeddings.embed_query(text_chunks[0].page_content))
