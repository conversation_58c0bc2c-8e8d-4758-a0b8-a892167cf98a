Hey everyone. 
Today, we're going to explore how to compute sentence similarity using the Hugging Face Transformers library. 
Sentence similarity is a powerful technique that can be used in chatbots to identify the intent of user input. 
Unlike traditional text classification, which assigns predefined categories to text, 
sentence similarity models can compare user input to a set of example sentences, and determine how closely they match. 
This approach allows chatbots to find the most similar response based on the highest similarity score. 
Additionally, sentence similarity can also be used to double-check the results of text classification models, 
providing a robust method for understanding and responding to user queries.  
Let's start.
First. Import the necessary modules.
from transformers, import Auto-Tokenizer, Auto-Model.
Auto-Tokenizer and Auto-Model from the transformers library are used to load pre-trained models and tokenizers.
Next,. import torch.
torch is used for tensor operations, which are crucial for handling large datasets efficiently.
Next,. import torch dot yen-yen, which is torch neural networks, dot functional, as N-N-F.
This provides a range of functions to perform various neural network operations.
This line imports the functional module from the torch dot n-n package and assigns it an alias N-N-F.
Next, from S-K-learn dot metrics dot pair-wise import cosine_similarity
This computes the cosine similarity between vectors, which helps in comparing sentence embeddings.
Next, Load model from Hugging-Face Hub.
Here, we're going to use all-Mini-L-M-L-SIX-we-two, a lightweight model optimized for sentence embeddings.
tokenizer equals-to Auto-Tokenizer dot from-pretrained 'sentence-transformers slash model name'.
model equals-to Auto-Model dot from_pretrained 'sentence-transformers slash model name'
Next,. define some sentences we want to compare. let's define some sentences for a bank chatbot for which we want to generate embeddings.
These sentences will be used to compare against the input sentence and find the most similar ones.
Next,. define a mean pooling function, to handle embeddings.
deaf mean_pooling. which takes two arguments. model_output and attention_mask.
token_embeddings equals to First element of model_output which contains all token embeddings.
next,. input_mask_expanded equals to attention_mask dot unsqueeze of minus one dot expand.token_embeddings dot size. dot float.
attention_mask dot unsqueeze of minus one, Adds an extra dimension to the attention mask tensor.
expand, token_embeddings dot size, Expands the attention mask to match the dimensions of token_embeddings.
and then, float Converts the expanded mask to a float tensor.
finally return, torch dot sum. token_embeddings into input_mask_expanded, comma one. 
divided by, torch dot clamp.(input_mask_expanded dot sum of one. min equals to one-E Nine.
token_embeddings contains the vectors representing each word in the sentence.
input_mask_expanded is a mask where non-padding tokens are one and padding tokens are zero.
Multiplying these element-wise means that only the embeddings of the actual words which is non-padding token, remain. while the embeddings of padding tokens are zeroed out.
torch dot sum. token_embeddings into input_mask_expanded, comma one.
This sums up the embeddings along the sequence length which is dimension one. 
This gives a single vector for each sentence which is the sum of the embeddings of the non-padding tokens.
And,. input_mask_expanded dot sum of one, counts the number of non-padding tokens in each sentence.
min equals to one-E Nine, ensures this count is at least one-E Nine to avoid division by zero.
Dividing the summed embeddings by the count of non-padding tokens gives the average, that is. mean embedding for each sentence.
The mean_pooling function is used to compute the average of token embeddings from a model output while considering the attention mask. 
This pooling method ensures that padding tokens are not included in the average, which is crucial for accurately representing the sentence embedding.
Next,. we are going to define the core function of our process, the compute_similarity function. 
This function takes an input sentence and calculates the similarity scores between it and a list of predefined sentences.
deaf compute_similarity and pass the input_sentence.
Next,. Tokenize the input-sentence.
encoded_input equals to tokenizer. pass the input_sentence, padding equals to True, truncation equals to True, return_tensors equals to 'P_T'.
This line uses the tokenizer to tokenize the input_sentence. 
The padding equals_to True, and truncation equals_to True parameters ensure that the input is appropriately padded and truncated to match the model's expected input size. 
The return_tensors equals_to 'P-T' parameter specifies that the output should be in the form of PyTorch tensors.
Next,. pass the tokenized input sentence through the model to obtain the model's output.
with torch dot no_grad. input_model_output equals_to model, double_star encoded_input.
Here, we use the torch dot no_grad context manager to disable gradient calculations, 
which saves memory and speeds up the computation since we are only interested in the forward pass.
Next,. compute the sentence embedding for the input sentence.
input_sentence_embedding equals_to mean_pooling. input_model_output, and, attention_mask from encoded_input.
This line calls the mean_pooling function to compute the sentence embedding for the input_sentence. 
The function takes the input_model_output and the attention_mask from encoded_input to generate a pooled embedding, representing the input sentence as a fixed-size vector.
Next,. normalize the computed sentence embedding.
input_sentence_embedding equals_to 'YEN-YEN-EF' dot normalize. input_sentence_embedding, PEE equals_to two , dim equals_to one.
The computed sentence embedding is normalized to have a unit norm. 
Normalization ensures that the cosine similarity calculations are meaningful. 
The PEE equals_to two parameter indicates the El-Two norm, which means, (Euclidean norm), and dim equals_to one, specifies the dimension along which to normalize.
Next,. tokenize the list of predefined example sentences.
encoded_sentences equals_to tokenizer. (sentences, padding equals_to True, truncation equals_to True, return_tensors equals_to 'P-T').
This line tokenizes the list of predefined example sentences using the same tokenizer settings as for the input sentence.
Next, pass the tokenized example sentences through the model to obtain their corresponding outputs.
with torch dot no_grad. sentences_model_output equals_to model, double_star encoded_sentences.
We pass the tokenized example sentences through the model to obtain their corresponding outputs, again using torch dot no_grad to disable gradient calculations.
Next,. compute the embeddings for the example sentences.
sentences_embeddings equals_to mean_pooling. (sentences_model_output, attention_mask from encoded_sentences).
We use the mean_pooling function to compute the embeddings for the example sentences, just like we did for the input sentence.
Next,. normalize the embeddings of the example sentences.
sentences_embeddings equals_to 'YEN-YEN-EF' dot normalize. sentences_embeddings, PEE equals_to two , dim equals_to one.
The embeddings of the example sentences are normalized to have unit norms, ensuring that the similarity calculations are on a consistent scale.
Next, compute the cosine similarity between the input sentence embedding and the example sentence embeddings.
similarities equals_to cosine_similarity.(input_sentence_embedding, sentences_embeddings).
This line computes the cosine similarity between the normalized embedding of the input sentence and the normalized embeddings of the example sentences. 
The result is a similarity matrix where each value represents the similarity score between the input sentence and an example sentence.
Next,. pair each example sentence with its corresponding similarity score.
sentences_with_scores equals_to list.zip of, sentences, index zero of similarities.
Here, we pair each example sentence with its corresponding similarity score by zipping the sentences list with the first row of the similarities matrix.
Next,. sort the list of sentences and their similarity scores in descending order.
sorted_sentences equals_to sorted. sentences_with_scores, key equals_to lambda ex, colon index one of ex, reverse equals_to True.
We sort the list of sentences and their similarity scores in descending order based on the similarity scores. 
This allows us to rank the sentences from most similar to least similar relative to the input sentence.
Finally, print the sorted sentences along with their similarity scores.
print.Similarity scores,in descending order.
for eye comma (sentence, score) in enumerate of (sorted_sentences, start equals_to 1).
print Similarity with sentence eye, colon sentence, hyphen, Score colon score.
Each sentence is printed with its rank and similarity score, showing how similar it is to the input sentence.















