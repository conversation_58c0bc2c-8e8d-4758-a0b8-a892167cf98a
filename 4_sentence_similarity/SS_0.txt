Hey everyone. Today we are going to explore how to compute Sentence-Similarity using the Hugging-Face Transformers library. 
We will use a pre-trained model, specifically the 'all-Mini-L-M-L-SIX-we-two' model, to generate embeddings for input sentences and a list of example sentences. 
Then, we will calculate the cosine similarity between the input sentence and each example sentence to determine their similarity scores. 
Finally, we will sort the example sentences based on their similarity scores in descending order and present the results.
Let's start.
First. Import the necessary modules.
from transformers, import Auto-Tokenizer, Auto-Model.
import torch.
import torch dot n-n, which is torch neural networks, dot functional, as N-N-F.
This line imports the functional module from the torch dot n-n package and assigns it an alias N-N-F.
Next, from S-K-learn dot metrics dot pair-wise import cosine_similarity
Next, Load model from Hugging-Face Hub
tokenizer equals-to Auto-Tokenizer dot from-pretrained 'sentence-transformers slash all-Mini-L-M-L-SIX-we-two'.
model equals-to Auto-Model dot from_pretrained 'sentence-transformers slash all-Mini-L-M-L-SIX-we-two'
