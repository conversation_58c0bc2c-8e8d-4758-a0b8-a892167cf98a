1
00:00:03,200 --> 00:00:05,670
Did you know Python has been around for more

2
00:00:05,680 --> 00:00:06,480
than 30 years

3
00:00:06,800 --> 00:00:09,110
and yet it is one of the most popular

4
00:00:09,120 --> 00:00:10,960
programming languages in the world.

5
00:00:11,520 --> 00:00:13,910
So it is no wonder that most companies,

6
00:00:13,920 --> 00:00:15,120
even man companies,

7
00:00:15,520 --> 00:00:17,430
ask questions on Python in their

8
00:00:17,440 --> 00:00:18,080
interviews.

9
00:00:18,480 --> 00:00:20,470
And if you are someone who is looking for a

10
00:00:20,480 --> 00:00:22,960
job as a data analyst, data scientist,

11
00:00:23,200 --> 00:00:24,720
or even as a developer, then

12
00:00:25,040 --> 00:00:26,080
this video is for you.

13
00:00:26,640 --> 00:00:29,670
In this video, we'll cover top 21 Python

14
00:00:29,680 --> 00:00:30,960
conceptual questions

15
00:00:31,200 --> 00:00:33,440
that are often asked in Python interviews.

16
00:00:33,680 --> 00:00:36,390
This video is part of our new series on

17
00:00:36,400 --> 00:00:37,520
interview preparation,

18
00:00:37,840 --> 00:00:38,760
where we'll bring to you

19
00:00:39,120 --> 00:00:41,430
valuable material on programming, data

20
00:00:41,440 --> 00:00:42,880
science and generative AI.

21
00:00:43,280 --> 00:00:45,040
So do subscribe to our channel

22
00:00:45,280 --> 00:00:46,720
so that you get free access

23
00:00:46,960 --> 00:00:48,790
to all of our learning material that is

24
00:00:48,800 --> 00:00:50,480
coming to you as part of the series.

25
00:00:51,040 --> 00:00:51,760
On this note,

26
00:00:52,000 --> 00:00:54,560
let's begin our discussion on the top 21

27
00:00:54,640 --> 00:00:55,720
Python interview questions.

28
00:00:56,760 --> 00:00:58,710
All right, our first question is why is

29
00:00:58,720 --> 00:01:01,360
Python called an interpreted language?

30
00:01:02,000 --> 00:01:04,350
Interpreted language means any programming

31
00:01:04,360 --> 00:01:06,640
language which executes instructions

32
00:01:06,800 --> 00:01:07,440
directly

33
00:01:07,680 --> 00:01:08,720
and line by line.

34
00:01:09,120 --> 00:01:11,110
It means it does not compile the program

35
00:01:11,120 --> 00:01:13,040
like C or Java does.

36
00:01:13,280 --> 00:01:15,510
Rather, it directly runs the complete

37
00:01:15,520 --> 00:01:16,000
program

38
00:01:16,320 --> 00:01:17,040
all at once.

39
00:01:17,600 --> 00:01:19,670
And while running the program, wherever it

40
00:01:19,680 --> 00:01:21,760
encounters an error, it stops.

41
00:01:22,320 --> 00:01:24,070
And if you think about it, this makes

42
00:01:24,080 --> 00:01:25,440
Python easier to debug.

43
00:01:25,840 --> 00:01:28,190
Because you get to know exactly where the

44
00:01:28,200 --> 00:01:28,960
error occurred

45
00:01:29,200 --> 00:01:29,920
in your code.

46
00:01:30,800 --> 00:01:32,320
Now Python doesn't need

47
00:01:32,560 --> 00:01:35,760
a separate compilation step like C or Java.

48
00:01:36,160 --> 00:01:38,630
You can just write Python code and run it

49
00:01:38,640 --> 00:01:39,200
directly.

50
00:01:39,280 --> 00:01:40,320
Here's what it means.

51
00:01:40,560 --> 00:01:42,640
C or Java is required

52
00:01:42,880 --> 00:01:44,320
to be built and linked.

53
00:01:44,640 --> 00:01:47,270
Building process involves using a compiler

54
00:01:47,280 --> 00:01:49,680
to translate the human related source code

55
00:01:49,920 --> 00:01:52,560
into machine readable object code files.

56
00:01:53,120 --> 00:01:54,400
The linking process

57
00:01:54,640 --> 00:01:56,640
combines these object code files

58
00:01:56,880 --> 00:01:59,520
together into a final executable program.

59
00:01:59,760 --> 00:02:02,870
These steps are necessary in languages like

60
00:02:02,880 --> 00:02:04,400
C or Java

61
00:02:04,640 --> 00:02:06,960
to produce an executable program

62
00:02:07,200 --> 00:02:08,080
that a computer can

63
00:02:08,400 --> 00:02:09,280
thereafter run.

64
00:02:10,040 --> 00:02:11,910
On the other hand, in Python you can

65
00:02:11,920 --> 00:02:14,240
directly run the code without going into

66
00:02:14,400 --> 00:02:16,080
any of these separate steps.

67
00:02:16,640 --> 00:02:19,200
You can just write Python code and run it

68
00:02:19,280 --> 00:02:19,840
directly.

69
00:02:20,240 --> 00:02:21,760
This is the precise reason why

70
00:02:22,000 --> 00:02:24,400
Python is fast to develop as well.

71
00:02:24,880 --> 00:02:26,800
Now let's move on to our next question.

72
00:02:26,880 --> 00:02:29,200
Do we need indentation in Python?

73
00:02:29,600 --> 00:02:31,200
The obvious answer is yes.

74
00:02:31,600 --> 00:02:33,910
You need indentation because it is part of

75
00:02:33,920 --> 00:02:35,280
the language's syntax.

76
00:02:35,440 --> 00:02:36,960
Now let's understand why.

77
00:02:37,440 --> 00:02:39,430
Indentation helps in reading the code

78
00:02:39,440 --> 00:02:39,800
better,

79
00:02:40,080 --> 00:02:41,990
and that is why Python has made it

80
00:02:42,000 --> 00:02:42,720
compulsory.

81
00:02:43,040 --> 00:02:44,710
Every programming language provides

82
00:02:44,720 --> 00:02:47,430
mechanism to manage and control how the

83
00:02:47,440 --> 00:02:48,640
coding blocks are written.

84
00:02:49,200 --> 00:02:50,960
In Python it is indentation

85
00:02:51,280 --> 00:02:52,800
and it is different in Java.

86
00:02:53,120 --> 00:02:54,560
For example, in Java when

87
00:02:54,880 --> 00:02:56,640
we need to construct a for loop,

88
00:02:56,880 --> 00:02:59,110
we have to put the blocks or statements

89
00:02:59,120 --> 00:03:01,520
inside the for loop within the braces

90
00:03:02,000 --> 00:03:04,390
and the statements inside the for loop need

91
00:03:04,400 --> 00:03:05,840
not be indented.

92
00:03:06,240 --> 00:03:07,600
It can be in the same line.

93
00:03:08,240 --> 00:03:10,150
But when it comes to Python, the print

94
00:03:10,160 --> 00:03:12,550
statement inside the for loop should have

95
00:03:12,560 --> 00:03:14,560
spaces, that is 4 spaces.

96
00:03:15,200 --> 00:03:17,440
That is how indentation works in Python.

97
00:03:17,800 --> 00:03:19,670
All right, now let's move on to the third

98
00:03:19,680 --> 00:03:20,160
question.

99
00:03:20,400 --> 00:03:22,960
What are built-in data types in Python?

100
00:03:23,600 --> 00:03:26,550
In Python, a data type defines the kind of

101
00:03:26,560 --> 00:03:28,560
values a piece of data can have

102
00:03:28,800 --> 00:03:31,040
and what operations can be performed on it.

103
00:03:31,280 --> 00:03:32,800
Let's understand in this way.

104
00:03:33,120 --> 00:03:35,280
Imagine different kinds of containers,

105
00:03:35,520 --> 00:03:36,600
a jar, a jug, a

106
00:03:36,880 --> 00:03:38,080
box, and a basket.

107
00:03:38,400 --> 00:03:40,550
Now, each container is suitable for holding

108
00:03:40,560 --> 00:03:41,840
different kinds of things.

109
00:03:42,080 --> 00:03:44,320
You wouldn't put water in a basket, right?

110
00:03:44,960 --> 00:03:46,320
It's for things like fruits.

111
00:03:46,400 --> 00:03:48,550
Similarly, in Python you use different data

112
00:03:48,560 --> 00:03:51,030
types to hold different kinds of values

113
00:03:51,040 --> 00:03:52,720
like integers or strings.

114
00:03:53,200 --> 00:03:55,910
Now let us look at various data types we

115
00:03:55,920 --> 00:03:56,640
have in Python.

116
00:03:57,120 --> 00:03:58,320
We have text type,

117
00:03:58,880 --> 00:04:01,430
we have numeric types, sequence types, map

118
00:04:01,440 --> 00:04:04,310
types, set types, boolean type, binary

119
00:04:04,320 --> 00:04:05,680
types, and none type.

120
00:04:06,000 --> 00:04:07,550
And in Python, in case you want to know the

121
00:04:07,560 --> 00:04:08,800
data type of a variable,

122
00:04:09,040 --> 00:04:10,400
just use the type function.

123
00:04:10,480 --> 00:04:11,440
Let's see how it works.

124
00:04:11,920 --> 00:04:15,200
Now in output, you will see the data type

125
00:04:15,240 --> 00:04:16,080
of the variable.

126
00:04:16,720 --> 00:04:18,470
The type function basically takes the

127
00:04:18,480 --> 00:04:21,510
variable as an argument and gives the data

128
00:04:21,520 --> 00:04:22,640
type of the variable.

129
00:04:22,960 --> 00:04:24,390
All right, let's move on to our 4th

130
00:04:24,400 --> 00:04:24,880
question.

131
00:04:25,200 --> 00:04:27,920
What are classes and objects in Python?

132
00:04:28,640 --> 00:04:29,360
In Python,

133
00:04:29,600 --> 00:04:32,150
a class is a blueprint for creating

134
00:04:32,160 --> 00:04:32,800
objects.

135
00:04:33,280 --> 00:04:35,840
On the other hand, an object is an instance

136
00:04:35,880 --> 00:04:36,640
of a class.

137
00:04:37,240 --> 00:04:38,160
What do I mean by this?

138
00:04:38,400 --> 00:04:40,710
Well, class can be considered to be your

139
00:04:40,720 --> 00:04:43,110
blueprint, and objects are real world

140
00:04:43,120 --> 00:04:44,080
entities which are

141
00:04:44,320 --> 00:04:46,480
defined or created from classes.

142
00:04:46,880 --> 00:04:48,000
Let's take an example here.

143
00:04:48,320 --> 00:04:50,640
This is a blueprint of a house.

144
00:04:50,880 --> 00:04:52,790
The blueprint defines the layout of the

145
00:04:52,800 --> 00:04:54,320
house, the number of rooms, the

146
00:04:54,560 --> 00:04:56,710
type of rooms including kitchen, bathroom,

147
00:04:56,720 --> 00:04:57,680
bedroom, etc,

148
00:04:57,920 --> 00:04:59,680
the material used, etc.

149
00:04:59,800 --> 00:05:02,480
And now with this blueprint we can create

150
00:05:02,560 --> 00:05:04,480
an unlimited number of houses.

151
00:05:04,840 --> 00:05:06,880
An object, on the other hand, is an

152
00:05:07,040 --> 00:05:08,720
instance of a class.

153
00:05:09,040 --> 00:05:11,830
It's a real tangible thing that you create

154
00:05:11,840 --> 00:05:12,960
using the blueprint.

155
00:05:13,600 --> 00:05:16,960
Over here it is House 1, House 2 and House 3

156
00:05:18,320 --> 00:05:20,320
are in fact objects, actual houses

157
00:05:20,560 --> 00:05:22,550
created from the House class, which is the

158
00:05:22,560 --> 00:05:23,120
blueprint.

159
00:05:23,280 --> 00:05:24,870
They have the structure defined by the

160
00:05:24,880 --> 00:05:25,680
House class.

161
00:05:25,840 --> 00:05:27,840
Now let's move on to our fifth question,

162
00:05:28,040 --> 00:05:30,080
what is a dictionary in Python?

163
00:05:30,320 --> 00:05:32,550
Dictionary is an unordered collection of

164
00:05:32,560 --> 00:05:33,120
elements.

165
00:05:33,680 --> 00:05:35,910
In other words, a dictionary in Python is a

166
00:05:35,920 --> 00:05:37,680
built in data structure for storing

167
00:05:37,760 --> 00:05:40,000
collections of objects or values.

168
00:05:40,480 --> 00:05:42,710
These elements in a dictionary are stored

169
00:05:42,720 --> 00:05:44,000
as key value pairs.

170
00:05:44,240 --> 00:05:46,000
This means there is a specific key

171
00:05:46,400 --> 00:05:48,960
and there is a value which is assigned to a key.

172
00:05:49,440 --> 00:05:51,120
This is pretty much like a real

173
00:05:51,440 --> 00:05:52,720
life dictionary where

174
00:05:53,040 --> 00:05:54,240
you look up a word

175
00:05:54,560 --> 00:05:55,600
known as a key

176
00:05:55,840 --> 00:05:58,590
to get its definition on a specific page

177
00:05:58,600 --> 00:05:59,920
which is known as the value.

178
00:06:00,280 --> 00:06:01,990
For example, in the dictionary, my

179
00:06:02,000 --> 00:06:04,160
dictionary over here, the key is name

180
00:06:04,640 --> 00:06:05,920
and the value

181
00:06:06,320 --> 00:06:08,000
for the key is John.

182
00:06:08,240 --> 00:06:09,200
In this example,

183
00:06:09,680 --> 00:06:12,960
name, age, city are the keys

184
00:06:13,280 --> 00:06:15,630
and John 30 and New York are their

185
00:06:15,640 --> 00:06:16,800
respective values.

186
00:06:17,280 --> 00:06:19,200
This dictionary represents a person

187
00:06:19,440 --> 00:06:22,390
and we can use it to look up details about

188
00:06:22,400 --> 00:06:24,640
the person like their name, age and city.

189
00:06:24,880 --> 00:06:26,910
By the way, dictionaries are mutable in

190
00:06:26,920 --> 00:06:27,350
nature.

191
00:06:27,360 --> 00:06:30,000
You can modify the dictionary by adding,

192
00:06:30,240 --> 00:06:32,880
changing, or removing key value pairs,

193
00:06:33,200 --> 00:06:34,440
as shown in this example.

194
00:06:34,920 --> 00:06:36,720
All right, let's move on to question number six.

195
00:06:37,040 --> 00:06:38,560
What are Python functions

196
00:06:38,800 --> 00:06:41,200
and how do they help in code optimization?

197
00:06:41,520 --> 00:06:43,200
A function is a block of code

198
00:06:43,440 --> 00:06:46,070
that can be called by other parts of your

199
00:06:46,080 --> 00:06:46,560
program.

200
00:06:46,800 --> 00:06:49,110
In other words, it's a piece of reusable

201
00:06:49,120 --> 00:06:51,520
code that you can run whenever you need it.

202
00:06:51,920 --> 00:06:54,800
Instead of writing the same code multiple times.

203
00:06:55,200 --> 00:06:56,640
So how do you write a function?

204
00:06:57,360 --> 00:06:59,830
First, you define functions using the def

205
00:06:59,840 --> 00:07:02,080
keyword followed by the function name,

206
00:07:02,240 --> 00:07:05,040
parenthesis and a colon followed by it.

207
00:07:05,520 --> 00:07:08,000
The code block within every function is

208
00:07:08,080 --> 00:07:08,800
indented

209
00:07:09,280 --> 00:07:11,440
and this is how a function looks like.

210
00:07:11,760 --> 00:07:14,960
In this example, addnumbers is a function

211
00:07:15,200 --> 00:07:16,640
that takes 2 parameters,

212
00:07:16,880 --> 00:07:18,240
#1 and #2,

213
00:07:18,480 --> 00:07:21,040
adds them together and returns the result.

214
00:07:21,400 --> 00:07:23,760
When we call add numbers three

215
00:07:24,400 --> 00:07:24,880
five,

216
00:07:25,200 --> 00:07:27,590
it executes the code within the function

217
00:07:27,600 --> 00:07:29,350
and returns the sum of three and five,

218
00:07:29,360 --> 00:07:30,080
which is 8.

219
00:07:30,240 --> 00:07:32,320
So how does it help in code optimization?

220
00:07:32,640 --> 00:07:32,960
Well,

221
00:07:33,200 --> 00:07:35,750
functions allow you to reuse code by

222
00:07:35,760 --> 00:07:38,400
encapsulating it in a single place

223
00:07:38,640 --> 00:07:40,390
and calling it multiple times from

224
00:07:40,400 --> 00:07:41,920
different parts of your program.

225
00:07:42,320 --> 00:07:45,270
In this example, say hello is a function

226
00:07:45,280 --> 00:07:46,960
that doesn't take any parameters.

227
00:07:47,280 --> 00:07:48,720
When we call say hello,

228
00:07:49,120 --> 00:07:50,800
it simply prints hello world.

229
00:07:51,200 --> 00:07:53,360
And what else? Well, functions give

230
00:07:53,440 --> 00:07:55,600
improved readability to your program.

231
00:07:55,920 --> 00:07:58,480
By dividing your code into logical blocks,

232
00:07:58,720 --> 00:08:01,270
functions can make your code more readable

233
00:08:01,280 --> 00:08:02,760
and easier to understand.

234
00:08:03,280 --> 00:08:06,320
It also reduces redundancy of your code

235
00:08:06,560 --> 00:08:08,640
and makes it cleaner and readable.

236
00:08:08,720 --> 00:08:10,950
Functions also make code easier for

237
00:08:10,960 --> 00:08:11,520
testing.

238
00:08:11,680 --> 00:08:13,550
Functions allow you to test individual

239
00:08:13,560 --> 00:08:14,480
blocks of code

240
00:08:14,720 --> 00:08:15,520
separately,

241
00:08:15,920 --> 00:08:18,230
so you can debug individual functions

242
00:08:18,240 --> 00:08:20,240
independently of the rest of the code.

243
00:08:20,720 --> 00:08:23,200
It also provides improved performance.

244
00:08:23,280 --> 00:08:25,430
Functions can also help to improve the

245
00:08:25,440 --> 00:08:28,000
performance of your code by allowing you to

246
00:08:28,240 --> 00:08:30,480
use optimized code libraries

247
00:08:30,720 --> 00:08:31,920
or by allowing the

248
00:08:32,160 --> 00:08:34,070
Python interpreter to optimize the code

249
00:08:34,080 --> 00:08:35,040
more effectively.

250
00:08:35,280 --> 00:08:37,120
Now let's move on to question #7.

251
00:08:37,280 --> 00:08:38,560
What are Python sets?

252
00:08:38,880 --> 00:08:41,120
Explain some of the properties of sets.

253
00:08:41,280 --> 00:08:43,790
In Python, a set is an unordered collection

254
00:08:43,800 --> 00:08:44,800
of unique objects.

255
00:08:45,280 --> 00:08:48,000
Sets are defined using curly braces.

256
00:08:48,160 --> 00:08:50,310
Each and every value is separated by

257
00:08:50,320 --> 00:08:50,880
commas.

258
00:08:51,120 --> 00:08:52,950
And you know what is more interesting? It

259
00:08:52,960 --> 00:08:53,840
is not ordered.

260
00:08:54,080 --> 00:08:54,880
For example,

261
00:08:55,120 --> 00:08:56,240
we give input

262
00:08:56,480 --> 00:08:57,680
apple, banana,

263
00:08:57,920 --> 00:09:01,350
cherry and while giving output the order is

264
00:09:01,360 --> 00:09:01,910
changed.

265
00:09:01,920 --> 00:09:03,600
So as you can see it is not ordered.

266
00:09:03,920 --> 00:09:05,440
Similarly, sets are

267
00:09:05,760 --> 00:09:07,750
often used to store a collection of

268
00:09:07,760 --> 00:09:08,960
distinct objects.

269
00:09:09,040 --> 00:09:10,870
That means they automatically remove

270
00:09:10,880 --> 00:09:11,920
duplicate values.

271
00:09:12,720 --> 00:09:13,920
For example, over here

272
00:09:14,240 --> 00:09:15,360
we have defined a set

273
00:09:15,680 --> 00:09:16,160
having

274
00:09:16,400 --> 00:09:18,720
elements apple, banana, cherry, orange

275
00:09:19,520 --> 00:09:22,480
and if we add another element to this set

276
00:09:22,560 --> 00:09:24,550
in the output you will see apple is not

277
00:09:24,560 --> 00:09:25,440
repeated twice.

278
00:09:25,680 --> 00:09:27,390
Now sets are also used to perform

279
00:09:27,400 --> 00:09:29,670
membership tests, that is to check if an

280
00:09:29,680 --> 00:09:32,000
object is indeed present in a set

281
00:09:32,640 --> 00:09:34,480
and this is how it is done.

282
00:09:34,960 --> 00:09:37,760
And for this piece of code the output is yes

283
00:09:38,080 --> 00:09:39,120
because banana.

284
00:09:39,440 --> 00:09:39,840
Is

285
00:09:40,320 --> 00:09:41,480
definitely present in the

286
00:09:41,920 --> 00:09:42,560
fruit set.

287
00:09:42,720 --> 00:09:45,040
Now let's move on to the properties of sets.

288
00:09:45,600 --> 00:09:48,190
First one is sets are unordered as I

289
00:09:48,200 --> 00:09:48,800
explained.

290
00:09:49,360 --> 00:09:51,040
Secondly, sets are unique.

291
00:09:52,040 --> 00:09:54,870
Third is sets are mutable, so you can add

292
00:09:54,880 --> 00:09:57,590
or remove elements from a set using the add

293
00:09:57,600 --> 00:09:58,720
and remove methods.

294
00:09:59,200 --> 00:10:01,360
And fourthly, sets are not indexed.

295
00:10:01,680 --> 00:10:02,560
Sets do not

296
00:10:02,880 --> 00:10:04,720
support indexing or slicing,

297
00:10:04,960 --> 00:10:07,520
so you cannot access individual elements.

298
00:10:08,040 --> 00:10:09,520
Of a set using an index.

299
00:10:09,760 --> 00:10:11,680
All right, now let's move on to question #8.

300
00:10:11,840 --> 00:10:14,310
What is the difference between list and a

301
00:10:14,320 --> 00:10:16,990
tuple? List and tuple are essentially data

302
00:10:17,000 --> 00:10:17,600
structures.

303
00:10:17,840 --> 00:10:19,750
So let's first understand what data

304
00:10:19,760 --> 00:10:20,880
structures are in Python.

305
00:10:21,120 --> 00:10:23,240
Data structures are a way of storing and

306
00:10:23,280 --> 00:10:24,880
organizing data efficiently.

307
00:10:25,200 --> 00:10:26,360
Why is this important?

308
00:10:26,960 --> 00:10:29,390
When you store your data in an unorganized

309
00:10:29,400 --> 00:10:29,840
manner,

310
00:10:30,080 --> 00:10:31,520
this will allow you to

311
00:10:32,160 --> 00:10:34,800
easily access and perform operations on the data.

312
00:10:35,360 --> 00:10:37,830
Now you will want to store data in

313
00:10:37,840 --> 00:10:40,160
different ways to cater to the need of the hour.

314
00:10:40,480 --> 00:10:43,110
Maybe you want to store all data types

315
00:10:43,120 --> 00:10:45,280
together, or you want to store data in

316
00:10:45,520 --> 00:10:48,110
such a manner which will help in faster

317
00:10:48,120 --> 00:10:50,240
retrieval, or maybe in some ways that

318
00:10:50,480 --> 00:10:52,560
stores only distinct data items.

319
00:10:52,720 --> 00:10:55,150
Luckily, Python has a host of inbuilt data

320
00:10:55,160 --> 00:10:55,680
structures.

321
00:10:56,000 --> 00:10:56,560
Out of all,

322
00:10:56,800 --> 00:10:57,760
list and tuple

323
00:10:58,000 --> 00:11:00,640
are two types of built in data structures

324
00:11:00,880 --> 00:11:03,040
that are used to store collections of data.

325
00:11:03,200 --> 00:11:05,600
Now let's look at the differences.

326
00:11:05,840 --> 00:11:07,200
First is mutability.

327
00:11:07,440 --> 00:11:08,720
Lists are mutable.

328
00:11:08,800 --> 00:11:11,390
They can be modified after creation, so you

329
00:11:11,400 --> 00:11:14,320
can do adding, removing, changing items, etc.

330
00:11:14,920 --> 00:11:16,800
Whereas tuples are immutable.

331
00:11:17,120 --> 00:11:18,240
Once they are created,

332
00:11:18,480 --> 00:11:20,320
their content cannot be changed.

333
00:11:20,880 --> 00:11:22,640
Second difference is syntax.

334
00:11:22,960 --> 00:11:25,430
Lists are defined using square brackets as

335
00:11:25,440 --> 00:11:26,000
you can see,

336
00:11:26,240 --> 00:11:28,640
and tuples are defined using parentheses.

337
00:11:28,720 --> 00:11:29,520
Again you can see

338
00:11:30,480 --> 00:11:32,160
third key difference is performance.

339
00:11:32,480 --> 00:11:35,350
Lists take up more memory due to their

340
00:11:35,360 --> 00:11:37,830
ability to change and grow, whereas tuples

341
00:11:37,840 --> 00:11:39,120
are more memory efficient

342
00:11:39,360 --> 00:11:41,510
and usually faster because they are

343
00:11:41,520 --> 00:11:42,080
immutable.

344
00:11:42,280 --> 00:11:44,400
And this is how a list looks like.

345
00:11:45,200 --> 00:11:47,520
And this is how a tuple looks like.

346
00:11:47,680 --> 00:11:48,160
Alright,

347
00:11:48,400 --> 00:11:50,560
now let's move on to our question #9.

348
00:11:50,720 --> 00:11:53,600
What is the difference between a module and

349
00:11:53,640 --> 00:11:55,670
a package? Now, this is a very common

350
00:11:55,680 --> 00:11:57,280
question asked during interviews.

351
00:11:57,360 --> 00:11:59,760
Modules and packages in Python

352
00:12:00,160 --> 00:12:02,000
are responsible for implementing the

353
00:12:02,080 --> 00:12:03,680
modular programming paradigm.

354
00:12:04,080 --> 00:12:04,560
First,

355
00:12:04,800 --> 00:12:06,480
let's see what a module is.

356
00:12:06,640 --> 00:12:09,600
Module in Python is simply a dot PY file

357
00:12:09,840 --> 00:12:10,880
containing Python code.

358
00:12:11,760 --> 00:12:13,920
It can contain any number of functions,

359
00:12:14,000 --> 00:12:15,600
classes and variables.

360
00:12:16,000 --> 00:12:18,030
It is a very common practice to put

361
00:12:18,040 --> 00:12:18,560
together

362
00:12:18,800 --> 00:12:21,120
a set of related functionalities together

363
00:12:21,200 --> 00:12:23,760
so that you can reuse them whenever you want.

364
00:12:23,840 --> 00:12:24,880
Now how do you do that?

365
00:12:25,200 --> 00:12:27,230
Once a module is created, it can be

366
00:12:27,240 --> 00:12:30,440
imported and used in another Python script

367
00:12:30,640 --> 00:12:31,960
using the import statement.

368
00:12:32,480 --> 00:12:33,840
Once you use this syntax,

369
00:12:34,080 --> 00:12:36,950
this will import all the functions, classes

370
00:12:36,960 --> 00:12:39,110
and variables that are present inside that

371
00:12:39,120 --> 00:12:39,520
module.

372
00:12:39,920 --> 00:12:41,440
Let's see how a module is used.

373
00:12:41,760 --> 00:12:44,720
First, we'll create a file called math dot PY.

374
00:12:45,120 --> 00:12:47,310
This will contain functions of arithmetic

375
00:12:47,320 --> 00:12:49,040
operations, that is addition,

376
00:12:49,440 --> 00:12:51,470
then multiplication, subtraction and

377
00:12:51,480 --> 00:12:51,920
division.

378
00:12:52,240 --> 00:12:53,760
Then we create another file

379
00:12:54,080 --> 00:12:55,680
called arithmetic dot PY

380
00:12:56,240 --> 00:12:57,840
where we import the maths

381
00:12:58,240 --> 00:13:00,320
dot PY file at the very beginning.

382
00:13:01,280 --> 00:13:03,630
We do that by writing import maths as you

383
00:13:03,640 --> 00:13:04,160
can see.

384
00:13:04,560 --> 00:13:06,230
Now let's see how it works.

385
00:13:06,240 --> 00:13:08,640
We assign a value of two to X.

386
00:13:08,960 --> 00:13:10,160
And three to Y

387
00:13:10,400 --> 00:13:11,600
and thereafter we call

388
00:13:12,160 --> 00:13:14,950
add and multiply functions from the math

389
00:13:14,960 --> 00:13:15,920
module. And

390
00:13:16,160 --> 00:13:17,760
as you can see the output

391
00:13:18,000 --> 00:13:20,000
we get is what is expected

392
00:13:20,240 --> 00:13:20,920
5:00 and 6:00.

393
00:13:21,760 --> 00:13:23,950
Now what if I want to add more complex

394
00:13:23,960 --> 00:13:26,150
functionalities? For that I need to create

395
00:13:26,160 --> 00:13:26,880
another file

396
00:13:27,120 --> 00:13:29,360
and import it as another module.

397
00:13:30,000 --> 00:13:32,480
But just imagine how repetitive it would be

398
00:13:32,720 --> 00:13:35,760
if we have to import 10 such modules.

399
00:13:36,800 --> 00:13:38,560
Now there is a solution for that.

400
00:13:39,680 --> 00:13:42,390
We put together all the related modules

401
00:13:42,400 --> 00:13:43,760
inside a directory

402
00:13:44,080 --> 00:13:45,440
and we call it a package.

403
00:13:46,000 --> 00:13:47,830
So a package in simple terms is a

404
00:13:47,840 --> 00:13:49,200
collection of modules.

405
00:13:49,440 --> 00:13:51,670
It is a way of organizing related Python

406
00:13:51,680 --> 00:13:53,200
modules into a directory.

407
00:13:53,600 --> 00:13:54,320
Basically, it's a

408
00:13:54,800 --> 00:13:57,040
directory that contains multiple module

409
00:13:57,280 --> 00:13:57,760
files

410
00:13:58,240 --> 00:14:00,240
along with a standard file called

411
00:14:00,480 --> 00:14:03,070
init.py, which tells Python that the

412
00:14:03,080 --> 00:14:05,200
directory is indeed a package.

413
00:14:05,680 --> 00:14:07,760
Now let's move on to our question #10.

414
00:14:08,000 --> 00:14:09,040
What is indexing

415
00:14:09,440 --> 00:14:11,950
and what is negative indexing? We need to

416
00:14:11,960 --> 00:14:14,480
understand the concept of iterables first

417
00:14:14,560 --> 00:14:16,400
before we get into indexing.

418
00:14:16,800 --> 00:14:19,110
It is a special type of object in Python

419
00:14:19,120 --> 00:14:19,760
that can

420
00:14:20,200 --> 00:14:20,960
iterate over.

421
00:14:21,440 --> 00:14:22,480
What do I mean by that?

422
00:14:22,960 --> 00:14:25,790
Well, you can cross or go through all the

423
00:14:25,800 --> 00:14:27,950
different elements or entities contained

424
00:14:27,960 --> 00:14:28,360
within the

425
00:14:28,720 --> 00:14:29,200
object,

426
00:14:29,680 --> 00:14:32,310
and the object could be a list, a tuple, a

427
00:14:32,320 --> 00:14:33,840
string, dictionary, etc.

428
00:14:34,080 --> 00:14:36,670
Are all iterable in Python and yes, the

429
00:14:36,680 --> 00:14:38,710
iteration part could easily be achieved

430
00:14:38,720 --> 00:14:39,360
with the help of.

431
00:14:39,518 --> 00:14:40,118
For loops.

432
00:14:40,358 --> 00:14:42,308
So in simple words, iterables are something

433
00:14:42,318 --> 00:14:43,238
you can loop over.

434
00:14:43,318 --> 00:14:45,718
Now when we understand what iterables are,

435
00:14:45,958 --> 00:14:47,798
now let's focus on indexing.

436
00:14:48,198 --> 00:14:50,918
Indexing is basically a way to access

437
00:14:51,078 --> 00:14:53,158
individual elements or groups of

438
00:14:53,398 --> 00:14:53,878
elements

439
00:14:54,118 --> 00:14:55,318
in an iterable

440
00:14:55,638 --> 00:14:56,678
by their position.

441
00:14:57,238 --> 00:14:59,308
In other words, you can access a specific

442
00:14:59,318 --> 00:15:01,718
element of your choice within an iterable,

443
00:15:01,798 --> 00:15:02,598
such as a list,

444
00:15:02,918 --> 00:15:05,468
and do your operations with it depending on

445
00:15:05,478 --> 00:15:06,118
your needs.

446
00:15:06,318 --> 00:15:08,108
Let me show you how I have this list of

447
00:15:08,118 --> 00:15:09,798
strings with various fruit names.

448
00:15:10,198 --> 00:15:11,948
In the very beginning, Python allocates

449
00:15:11,958 --> 00:15:13,038
memory for the

450
00:15:13,398 --> 00:15:16,438
list and each element is indexed this way.

451
00:15:16,838 --> 00:15:19,148
In Python, indexing starts from zero as you

452
00:15:19,158 --> 00:15:19,638
can see.

453
00:15:19,798 --> 00:15:22,038
So if you have a list called mylist

454
00:15:22,278 --> 00:15:23,158
having elements

455
00:15:23,398 --> 00:15:24,998
like apple, banana, cherry,

456
00:15:25,238 --> 00:15:26,998
the index of apple is 0,

457
00:15:27,278 --> 00:15:29,158
banana is 1 and cherry is 2.

458
00:15:29,718 --> 00:15:32,188
And this indexing that we have discussed is

459
00:15:32,198 --> 00:15:33,078
the one which.

460
00:15:33,318 --> 00:15:35,828
Is generally used and is called positive

461
00:15:35,838 --> 00:15:36,438
indexing.

462
00:15:36,918 --> 00:15:38,358
Now what is negative indexing?

463
00:15:38,678 --> 00:15:40,798
Negative indexing simply means the

464
00:15:41,398 --> 00:15:44,118
last element of the collection has an index of

465
00:15:44,438 --> 00:15:44,758
one

466
00:15:44,998 --> 00:15:46,038
and the second last

467
00:15:46,358 --> 00:15:47,238
has 2 and so on.

468
00:15:47,638 --> 00:15:50,358
Now let's move on to question #11, which is

469
00:15:50,678 --> 00:15:53,398
explain the logical operators in Python.

470
00:15:54,198 --> 00:15:57,798
In Python, the logical operators AND, OR and NOT

471
00:15:58,198 --> 00:16:00,518
can be used to perform boolean operations.

472
00:16:00,678 --> 00:16:02,838
This is done in order to determine whether

473
00:16:02,918 --> 00:16:04,438
a condition is true or false.

474
00:16:04,598 --> 00:16:06,308
What do I mean by this? Well, in Python

475
00:16:06,318 --> 00:16:08,748
there can be situations where there are two

476
00:16:08,758 --> 00:16:11,158
or more conditions and we need to check

477
00:16:11,238 --> 00:16:13,468
which condition is true and which is false.

478
00:16:13,478 --> 00:16:15,708
And for these situations we use logical

479
00:16:15,718 --> 00:16:16,358
operations.

480
00:16:16,438 --> 00:16:18,748
There are three types of logical operators,

481
00:16:18,758 --> 00:16:20,358
AND, OR and NOT.

482
00:16:20,758 --> 00:16:22,918
The AND operator returns true if both

483
00:16:22,958 --> 00:16:23,558
operands

484
00:16:23,798 --> 00:16:24,358
are true.

485
00:16:25,158 --> 00:16:27,318
If either condition is false, it returns false

486
00:16:27,638 --> 00:16:28,278
like this.

487
00:16:28,678 --> 00:16:31,508
Then or operator returns true if at least

488
00:16:31,518 --> 00:16:33,158
one of the operand is true.

489
00:16:33,478 --> 00:16:36,518
If both conditions are false, it returns false,

490
00:16:36,918 --> 00:16:37,718
right? Thereafter,

491
00:16:38,038 --> 00:16:40,428
not operator returns the opposite of

492
00:16:40,438 --> 00:16:40,958
operand.

493
00:16:41,318 --> 00:16:43,478
If the condition is true, it returns false,

494
00:16:43,718 --> 00:16:45,948
and if the condition is false, it returns true.

495
00:16:45,958 --> 00:16:48,428
Logical operators are often used in control

496
00:16:48,438 --> 00:16:50,278
flow constructs such as if,

497
00:16:50,598 --> 00:16:53,068
elif and while statements to combine

498
00:16:53,078 --> 00:16:53,798
conditions

499
00:16:54,038 --> 00:16:56,108
and make decisions based on multiple

500
00:16:56,118 --> 00:16:56,678
criteria.

501
00:16:57,078 --> 00:16:58,958
Now let's move on to our question #12.

502
00:16:59,478 --> 00:17:02,038
Explain the use of Lambda expression in Python

503
00:17:02,278 --> 00:17:03,878
and when is it useful?

504
00:17:04,118 --> 00:17:06,908
Well, the Lambda expression or function is

505
00:17:06,918 --> 00:17:09,068
used to define a function without a name in

506
00:17:09,078 --> 00:17:09,478
Python.

507
00:17:10,038 --> 00:17:11,948
It is also known as anonymous function

508
00:17:11,958 --> 00:17:13,478
because it doesn't have a name

509
00:17:14,038 --> 00:17:15,238
like regular functions do.

510
00:17:15,318 --> 00:17:16,748
The difference is that in a regular

511
00:17:16,758 --> 00:17:18,598
function you have to define the function.

512
00:17:18,758 --> 00:17:21,108
Lambda is useful in situation where you

513
00:17:21,118 --> 00:17:23,958
need a small temporary function that you won't

514
00:17:24,518 --> 00:17:26,278
need to use elsewhere in your code.

515
00:17:26,518 --> 00:17:28,438
And this is how Lambda function looks.

516
00:17:28,518 --> 00:17:30,268
Lambda function can have any number of

517
00:17:30,278 --> 00:17:30,838
arguments,

518
00:17:31,158 --> 00:17:33,798
but can contain only a single expression.

519
00:17:33,878 --> 00:17:35,788
This means you have to write the entire

520
00:17:35,798 --> 00:17:36,838
thing in one line.

521
00:17:36,918 --> 00:17:39,108
One of the foremost use cases of Lambda is

522
00:17:39,118 --> 00:17:40,198
in functional programming,

523
00:17:40,798 --> 00:17:42,988
wherein it allows you to supply a function

524
00:17:42,998 --> 00:17:43,798
as a parameter

525
00:17:44,038 --> 00:17:46,268
to a different function, for example map,

526
00:17:46,278 --> 00:17:47,198
filter, etc.

527
00:17:47,398 --> 00:17:48,998
Now let's move on to question #13.

528
00:17:49,478 --> 00:17:51,398
Explain slicing in Python.

529
00:17:51,558 --> 00:17:54,348
Slicing in Python is a feature that allows

530
00:17:54,358 --> 00:17:57,478
you to access a subset or a slice of a list

531
00:17:57,718 --> 00:17:58,758
or a sequence.

532
00:17:59,238 --> 00:18:01,078
In other words, slicing a list

533
00:18:01,318 --> 00:18:04,148
gives you another list instead of a single

534
00:18:04,158 --> 00:18:04,598
element.

535
00:18:04,678 --> 00:18:05,398
This means

536
00:18:05,638 --> 00:18:09,238
each object in the list has a specific index.

537
00:18:09,478 --> 00:18:12,998
Slice specifies a start index and an

538
00:18:13,238 --> 00:18:14,518
end index of a list.

539
00:18:14,758 --> 00:18:17,718
For example, in the list apple has an index of

540
00:18:17,958 --> 00:18:20,358
0, banana has an index of one, cherry has

541
00:18:20,438 --> 00:18:21,638
index of two, and so on.

542
00:18:21,798 --> 00:18:24,148
Now I want the fruit, banana, cherry and

543
00:18:24,158 --> 00:18:25,078
date from the list.

544
00:18:25,238 --> 00:18:27,158
We would need to slice it out from the list.

545
00:18:27,398 --> 00:18:29,798
So we slice it out over here.

546
00:18:30,038 --> 00:18:32,348
By the way, keep in mind that the sublist

547
00:18:32,358 --> 00:18:35,068
returned will contain the element starting

548
00:18:35,078 --> 00:18:38,038
from the start index, but will exclude the

549
00:18:38,198 --> 00:18:39,358
end index element.

550
00:18:39,798 --> 00:18:42,108
So we see the output is basically banana,

551
00:18:42,118 --> 00:18:43,078
cherry and date.

552
00:18:43,638 --> 00:18:44,278
One more thing,

553
00:18:44,518 --> 00:18:46,828
if you leave out the start index, it will

554
00:18:46,838 --> 00:18:48,118
assume it to be 0,

555
00:18:48,518 --> 00:18:50,908
and if you leave out the end index again,

556
00:18:50,918 --> 00:18:53,558
the output will be the entire length of the list.

557
00:18:54,278 --> 00:18:56,188
All right, now let's move on to question #14.

558
00:18:56,198 --> 00:18:56,918
Explain

559
00:18:57,158 --> 00:18:59,518
the difference between mutable and

560
00:18:59,558 --> 00:19:01,238
immutable objects in Python.

561
00:19:01,718 --> 00:19:03,708
For this, let's first understand what an

562
00:19:03,718 --> 00:19:05,078
object is in Python.

563
00:19:05,558 --> 00:19:06,278
In Python,

564
00:19:06,518 --> 00:19:09,148
almost everything is an object, including

565
00:19:09,158 --> 00:19:11,788
numbers, strings, lists, dictionaries,

566
00:19:11,798 --> 00:19:12,518
functions,

567
00:19:12,758 --> 00:19:13,798
and even modules.

568
00:19:14,358 --> 00:19:17,478
Now, objects can be of two types, mutable

569
00:19:17,558 --> 00:19:18,598
and immutable.

570
00:19:18,918 --> 00:19:21,068
If you remember correctly, we have already

571
00:19:21,078 --> 00:19:22,038
talked about this.

572
00:19:22,398 --> 00:19:24,508
When we were talking about list being

573
00:19:24,518 --> 00:19:26,598
mutable and tuple being immutable.

574
00:19:26,758 --> 00:19:28,598
Now let me talk about this in detail.

575
00:19:28,838 --> 00:19:30,598
So what does mutable means?

576
00:19:31,078 --> 00:19:33,868
These are objects whose content or state

577
00:19:33,878 --> 00:19:36,358
can be changed after they are created.

578
00:19:36,918 --> 00:19:38,598
We have seen an example of a list

579
00:19:38,918 --> 00:19:40,758
which is a mutable object before.

580
00:19:40,998 --> 00:19:42,908
We have seen an example of a list which is

581
00:19:42,918 --> 00:19:43,878
a mutable object.

582
00:19:44,198 --> 00:19:44,678
Now

583
00:19:44,918 --> 00:19:46,788
let's see a different example with a

584
00:19:46,798 --> 00:19:47,398
dictionary.

585
00:19:47,558 --> 00:19:49,708
In this example we have a dictionary called

586
00:19:49,718 --> 00:19:50,198
student.

587
00:19:50,678 --> 00:19:53,228
And we modify the value associated with the

588
00:19:53,238 --> 00:19:55,958
key age from 25 to 26.

589
00:19:56,278 --> 00:19:59,238
We also add a new key value pair gender

590
00:19:59,478 --> 00:20:01,078
female to the dictionary.

591
00:20:01,318 --> 00:20:03,468
Because the dictionary is mutable, so its

592
00:20:03,478 --> 00:20:06,348
content can be changed and updated as

593
00:20:06,358 --> 00:20:06,748
needed.

594
00:20:06,758 --> 00:20:08,518
Moving on, what are immutable objects?

595
00:20:08,598 --> 00:20:11,068
Well, these are objects whose content or

596
00:20:11,078 --> 00:20:12,988
state cannot be changed after they are

597
00:20:12,998 --> 00:20:13,438
created.

598
00:20:13,638 --> 00:20:15,468
If you try to change the state, you will

599
00:20:15,478 --> 00:20:17,198
actually create a new object.

600
00:20:17,398 --> 00:20:19,398
We have seen how a tuple looks like.

601
00:20:19,718 --> 00:20:21,948
Another example of a mutable object is a

602
00:20:21,958 --> 00:20:22,438
string.

603
00:20:22,598 --> 00:20:24,438
As you can see in this code,

604
00:20:24,678 --> 00:20:27,228
trying to directly change the character at

605
00:20:27,238 --> 00:20:29,158
index zero in the string name

606
00:20:29,478 --> 00:20:32,508
will raise a type error because strings are

607
00:20:32,518 --> 00:20:33,158
immutable.

608
00:20:33,238 --> 00:20:35,788
Instead, we need to create a new string

609
00:20:35,798 --> 00:20:36,918
modified name

610
00:20:37,558 --> 00:20:39,788
with the desired change, which is replacing

611
00:20:39,798 --> 00:20:41,158
the first character with M.

612
00:20:41,398 --> 00:20:43,238
Now let's move on to question #15.

613
00:20:43,438 --> 00:20:45,878
What is the use of pass keyword in Python?

614
00:20:46,038 --> 00:20:48,668
So pass is a null statement that basically

615
00:20:48,678 --> 00:20:49,398
does nothing.

616
00:20:49,878 --> 00:20:52,358
It is often used as a placeholder

617
00:20:52,598 --> 00:20:55,078
where a statement is required syntactically

618
00:20:55,478 --> 00:20:57,318
but no action needs to be taken.

619
00:20:57,478 --> 00:20:59,388
So basically it's just there to ensure your

620
00:20:59,398 --> 00:21:00,118
code runs

621
00:21:00,358 --> 00:21:03,308
without syntax errors until you are ready

622
00:21:03,318 --> 00:21:05,398
to write the real code for that block.

623
00:21:05,478 --> 00:21:06,198
Let me show you how.

624
00:21:06,758 --> 00:21:09,558
In this case, pass allows you to define the

625
00:21:09,638 --> 00:21:10,998
function myfunction

626
00:21:11,238 --> 00:21:12,678
without any actual code.

627
00:21:13,078 --> 00:21:15,308
This can be useful when you are sketching

628
00:21:15,318 --> 00:21:17,948
out a program and aren't ready to write the

629
00:21:17,958 --> 00:21:18,678
function yet.

630
00:21:18,838 --> 00:21:20,598
Now let's move on to question #16.

631
00:21:20,958 --> 00:21:22,118
What do generators

632
00:21:22,358 --> 00:21:24,828
tell about their use in Python? Suppose you

633
00:21:24,838 --> 00:21:27,548
want to make a huge list of numbers from 1

634
00:21:27,558 --> 00:21:28,438
to a billion.

635
00:21:28,918 --> 00:21:30,748
If you create a list for this, it will

636
00:21:30,758 --> 00:21:33,108
consume a lot of your computer's memory,

637
00:21:33,118 --> 00:21:33,638
which is

638
00:21:33,878 --> 00:21:35,398
not efficient at all.

639
00:21:36,038 --> 00:21:38,108
Instead, what you can do is you can create

640
00:21:38,118 --> 00:21:41,068
a generator that produces these numbers one

641
00:21:41,078 --> 00:21:43,638
at a time and only when they are needed.

642
00:21:44,358 --> 00:21:45,318
This way you

643
00:21:45,878 --> 00:21:48,508
only need memory for one number at a time,

644
00:21:48,518 --> 00:21:50,438
not all billion numbers together.

645
00:21:51,158 --> 00:21:53,388
Well, the official definition goes

646
00:21:53,398 --> 00:21:54,118
something like this.

647
00:21:54,278 --> 00:21:56,428
A generator in Python returns an iterator

648
00:21:56,438 --> 00:22:00,198
that produces sequences of values one at a time.

649
00:22:00,758 --> 00:22:03,558
We use the yield keyword to produce values.

650
00:22:03,638 --> 00:22:05,158
Now let's see how it works.

651
00:22:05,558 --> 00:22:06,518
In this example,

652
00:22:06,838 --> 00:22:09,788
countupto is a generator that counts from

653
00:22:09,798 --> 00:22:10,838
one up to

654
00:22:11,078 --> 00:22:12,358
the value of north.

655
00:22:12,838 --> 00:22:15,548
The yield keyword is used to specify what

656
00:22:15,558 --> 00:22:17,228
value the generator should produce when

657
00:22:17,238 --> 00:22:18,358
it's iterated over.

658
00:22:18,598 --> 00:22:21,068
Then it pauses the function until the next

659
00:22:21,078 --> 00:22:22,118
value is needed.

660
00:22:22,598 --> 00:22:24,428
The function state, including local

661
00:22:24,438 --> 00:22:26,998
variable, is saved between yields

662
00:22:27,238 --> 00:22:29,318
so it can pick up where it left off.

663
00:22:30,318 --> 00:22:33,078
When you use the generator in a for loop

664
00:22:33,558 --> 00:22:35,478
with count up to let's say 10,

665
00:22:35,718 --> 00:22:38,838
it prints the number one to 10 one at a time.

666
00:22:39,238 --> 00:22:42,268
It generates each number as the for loop

667
00:22:42,278 --> 00:22:44,188
asks for the next number rather than

668
00:22:44,198 --> 00:22:46,428
generating all the numbers at once, which

669
00:22:46,438 --> 00:22:48,278
would be less memory efficient.

670
00:22:48,598 --> 00:22:50,198
Moving on to question 17,

671
00:22:50,478 --> 00:22:52,708
what is shallow copy and deep copy in

672
00:22:52,718 --> 00:22:54,868
Python? Well, in Python, deep copy and

673
00:22:54,878 --> 00:22:57,068
shallow copy are methods used to copy

674
00:22:57,078 --> 00:22:57,558
objects.

675
00:22:57,758 --> 00:23:00,278
When we use the equal operator, it

676
00:23:00,518 --> 00:23:03,108
only creates a new variable that shares the

677
00:23:03,118 --> 00:23:04,758
reference of the original output.

678
00:23:04,838 --> 00:23:07,308
In order to create real copies or clones of

679
00:23:07,318 --> 00:23:08,038
these objects,

680
00:23:08,278 --> 00:23:10,598
we can use the copy module in Python.

681
00:23:10,998 --> 00:23:13,908
They are of two types, shallow copy and

682
00:23:13,918 --> 00:23:14,598
deep copy.

683
00:23:14,758 --> 00:23:16,678
Now let's focus on shallow copy first.

684
00:23:16,718 --> 00:23:17,718
A shallow copy is

685
00:23:17,998 --> 00:23:19,948
a copy of an object that stores the

686
00:23:19,958 --> 00:23:21,158
reference of the

687
00:23:21,398 --> 00:23:22,358
original elements.

688
00:23:22,438 --> 00:23:24,278
The simple way to understand this is

689
00:23:24,838 --> 00:23:27,558
it's like making a list of items you own.

690
00:23:27,798 --> 00:23:30,828
If any of those items is a box with more

691
00:23:30,838 --> 00:23:31,638
stuff inside,

692
00:23:31,878 --> 00:23:34,228
your list just mentions the box, not the

693
00:23:34,238 --> 00:23:35,078
stuff inside.

694
00:23:35,158 --> 00:23:37,068
So if you change the content of the box, it

695
00:23:37,078 --> 00:23:39,428
will be changed in both the original and

696
00:23:39,438 --> 00:23:39,958
the copy.

697
00:23:40,038 --> 00:23:41,558
Now let's come to deep copy.

698
00:23:41,918 --> 00:23:44,268
A deep copy is a process where we create a

699
00:23:44,278 --> 00:23:44,998
new object

700
00:23:45,238 --> 00:23:47,478
and copy elements recursively.

701
00:23:47,878 --> 00:23:50,468
The independent copy is created of the

702
00:23:50,478 --> 00:23:51,478
original object

703
00:23:51,798 --> 00:23:53,718
and its entire set of elements.

704
00:23:54,278 --> 00:23:56,868
Changes made in one object do not affect

705
00:23:56,878 --> 00:23:57,398
the other.

706
00:23:57,678 --> 00:23:58,678
What do I mean by this?

707
00:23:59,078 --> 00:24:00,918
It's like making a detailed list of

708
00:24:01,158 --> 00:24:02,118
items you own.

709
00:24:02,358 --> 00:24:04,838
If you have a box with more stuff inside of it,

710
00:24:05,078 --> 00:24:08,118
you list down all the stuff in the box too.

711
00:24:08,438 --> 00:24:10,908
So even if you change the contents of the

712
00:24:10,918 --> 00:24:13,548
box in the original list, it won't change

713
00:24:13,558 --> 00:24:14,198
in the copy.

714
00:24:14,598 --> 00:24:16,038
Let's move on to question #18.

715
00:24:16,238 --> 00:24:18,868
What is inheritance in Python? Also, what

716
00:24:18,878 --> 00:24:20,668
is the difference between single and

717
00:24:20,678 --> 00:24:21,798
multiple inheritance?

718
00:24:22,038 --> 00:24:24,348
Well, in Python, inheritance is a way to

719
00:24:24,358 --> 00:24:26,918
define a new class, also called a child class.

720
00:24:27,318 --> 00:24:29,748
That takes on attributes and methods from

721
00:24:29,758 --> 00:24:31,398
an existing parent class.

722
00:24:31,838 --> 00:24:32,838
What do I mean by this?

723
00:24:33,158 --> 00:24:35,548
Imagine a family where a child inherits

724
00:24:35,558 --> 00:24:38,518
traits from their parents, like their hair color

725
00:24:38,758 --> 00:24:39,398
or height.

726
00:24:39,478 --> 00:24:41,718
In Python, inheritance works similarly.

727
00:24:42,198 --> 00:24:44,428
A child class can inherit characteristics

728
00:24:44,438 --> 00:24:46,598
and behaviors from a parent class.

729
00:24:46,758 --> 00:24:48,598
Now there are two types of inheritance,

730
00:24:48,758 --> 00:24:50,038
single inheritance

731
00:24:50,278 --> 00:24:51,718
and multiple inheritance.

732
00:24:52,038 --> 00:24:54,628
Single inheritance is when a class inherits

733
00:24:54,638 --> 00:24:57,388
from a single superclass or a parent class.

734
00:24:57,398 --> 00:24:59,878
Think of it as a child who inherits

735
00:25:00,198 --> 00:25:01,558
traits from one parent.

736
00:25:01,638 --> 00:25:02,518
Let me show you how.

737
00:25:02,678 --> 00:25:03,398
In this code,

738
00:25:03,638 --> 00:25:06,038
child is inheriting from the parent.

739
00:25:06,358 --> 00:25:06,998
It means

740
00:25:07,238 --> 00:25:08,358
child class has

741
00:25:08,598 --> 00:25:11,388
all the methods that parent has, plus any

742
00:25:11,398 --> 00:25:12,438
additional methods

743
00:25:12,678 --> 00:25:13,438
as defined.

744
00:25:13,638 --> 00:25:16,198
Now let's see what multiple inheritance is.

745
00:25:16,438 --> 00:25:18,038
Multiple inheritance is when

746
00:25:18,358 --> 00:25:21,068
a class can inherit from more than one

747
00:25:21,078 --> 00:25:22,518
superclass or parent class.

748
00:25:22,598 --> 00:25:25,228
In our analogy, imagine a child who can

749
00:25:25,238 --> 00:25:27,398
inherit traits from both parents.

750
00:25:27,638 --> 00:25:30,308
In this case, child is inheriting both from

751
00:25:30,318 --> 00:25:31,878
the mother and the father,

752
00:25:32,198 --> 00:25:32,518
so it

753
00:25:32,758 --> 00:25:33,238
has

754
00:25:33,798 --> 00:25:36,518
access to the methods from both classes.

755
00:25:36,758 --> 00:25:38,828
The order of inheritance matters in

756
00:25:38,838 --> 00:25:40,038
multiple inheritance.

757
00:25:40,198 --> 00:25:42,908
So if both mother and father had a method

758
00:25:42,918 --> 00:25:43,878
with the same name,

759
00:25:44,118 --> 00:25:46,758
the method from mother would be used

760
00:25:46,998 --> 00:25:48,678
because mother appears first.

761
00:25:48,838 --> 00:25:50,588
Now let's move on to question 19.

762
00:25:50,598 --> 00:25:53,548
What is exception handling and how is it

763
00:25:53,558 --> 00:25:54,838
done in Python?

764
00:25:55,078 --> 00:25:57,718
Well, exception handling in Python involves

765
00:25:57,878 --> 00:25:59,638
using special blocks of code

766
00:26:00,358 --> 00:26:03,348
so that it can catch and handle errors or

767
00:26:03,358 --> 00:26:06,198
exceptions that occur when your program runs.

768
00:26:06,678 --> 00:26:08,748
Basically, it is done to help your program

769
00:26:08,758 --> 00:26:11,148
keep running even when something unexpected

770
00:26:11,158 --> 00:26:11,638
happens.

771
00:26:12,038 --> 00:26:13,078
The main keywords

772
00:26:13,638 --> 00:26:15,868
for exception handling in Python are try,

773
00:26:15,878 --> 00:26:17,958
except, finally, and raise.

774
00:26:18,118 --> 00:26:20,038
At this point, let me show you how it works

775
00:26:20,438 --> 00:26:21,158
in this code.

776
00:26:21,798 --> 00:26:24,628
If you enter a valid integer, it prints the

777
00:26:24,638 --> 00:26:25,718
number and

778
00:26:25,958 --> 00:26:28,598
this gets executed no matter what.

779
00:26:28,838 --> 00:26:30,748
Whereas if you enter something that's not a

780
00:26:30,758 --> 00:26:33,398
valid integer like a string or a float,

781
00:26:33,638 --> 00:26:35,318
it raises a value error.

782
00:26:35,878 --> 00:26:38,678
The exception that moves to the except block

783
00:26:39,318 --> 00:26:41,878
prints that's not a valid number

784
00:26:42,278 --> 00:26:45,798
and then this gets executed no matter what.

785
00:26:46,038 --> 00:26:48,748
The finally block is optional and is used

786
00:26:48,758 --> 00:26:50,998
for code that you want to be executed

787
00:26:51,318 --> 00:26:53,878
no matter whether an exception occurred or not.

788
00:26:54,438 --> 00:26:56,118
Now let's move on to question #20.

789
00:26:56,238 --> 00:26:58,598
What is the use of decorators in Python?

790
00:26:58,918 --> 00:27:01,468
In Python, decorators are a special kind of

791
00:27:01,478 --> 00:27:01,958
function

792
00:27:02,198 --> 00:27:04,428
that add extra functionality to another

793
00:27:04,438 --> 00:27:04,918
function.

794
00:27:05,198 --> 00:27:06,198
What do I mean by this?

795
00:27:06,598 --> 00:27:09,388
Well, they provide a way to wrap a function

796
00:27:09,398 --> 00:27:10,358
with another function

797
00:27:10,678 --> 00:27:12,278
which can add functionality

798
00:27:12,598 --> 00:27:15,718
before or after the original function is called

799
00:27:15,958 --> 00:27:17,718
without permanently modifying it.

800
00:27:17,798 --> 00:27:18,678
Let me show you how.

801
00:27:19,158 --> 00:27:20,358
When you call say hello,

802
00:27:20,598 --> 00:27:22,828
it's not directly called anymore, it's now

803
00:27:22,838 --> 00:27:25,628
wrapped with the wrapper function inside my

804
00:27:25,638 --> 00:27:26,278
decorator.

805
00:27:26,598 --> 00:27:26,758
The

806
00:27:27,318 --> 00:27:30,268
mydecorator syntax is just an easier way of

807
00:27:30,278 --> 00:27:31,638
saying say hello

808
00:27:31,878 --> 00:27:33,398
equal to mydecorator

809
00:27:33,638 --> 00:27:34,278
say hello.

810
00:27:34,438 --> 00:27:35,718
Now let's look at the output.

811
00:27:35,878 --> 00:27:38,428
In this example, mydecorator is a decorator

812
00:27:38,438 --> 00:27:40,438
that takes a function as input

813
00:27:40,838 --> 00:27:43,078
and defines a new function wrapper

814
00:27:43,318 --> 00:27:46,028
that adds some code before and after

815
00:27:46,038 --> 00:27:46,838
calling function.

816
00:27:47,078 --> 00:27:47,198
The

817
00:27:47,478 --> 00:27:49,878
at the rate mydecorator line in Python's

818
00:27:50,118 --> 00:27:52,788
decorator syntax for applying the decorator

819
00:27:52,798 --> 00:27:53,998
to the say hello function.

820
00:27:54,278 --> 00:27:56,278
Alright, now we come to our last question.

821
00:27:56,798 --> 00:27:59,078
Which is explain the difference between is

822
00:27:59,398 --> 00:28:01,478
and the equality operator in Python.

823
00:28:01,558 --> 00:28:04,028
The equality operator checks for value

824
00:28:04,038 --> 00:28:04,758
equality.

825
00:28:04,838 --> 00:28:07,068
It compares the values of the two objects

826
00:28:07,078 --> 00:28:09,158
and returns true if they are equal

827
00:28:09,478 --> 00:28:11,078
and false if they are not equal.

828
00:28:11,398 --> 00:28:13,948
It is basically a comparison operator that

829
00:28:13,958 --> 00:28:15,558
checks for equality.

830
00:28:16,118 --> 00:28:18,268
It compares the value of the two objects to

831
00:28:18,278 --> 00:28:19,718
determine if they are equal.

832
00:28:19,798 --> 00:28:22,308
In this case, list one and list two are two

833
00:28:22,318 --> 00:28:23,238
different objects.

834
00:28:23,558 --> 00:28:25,158
But they have the same value.

835
00:28:25,238 --> 00:28:26,198
Therefore list one

836
00:28:26,518 --> 00:28:26,918
is

837
00:28:27,238 --> 00:28:30,678
equal to list 2 is actually true. Now is

838
00:28:30,918 --> 00:28:32,358
checks for identity.

839
00:28:32,758 --> 00:28:35,788
It returns true if both variables point to

840
00:28:35,798 --> 00:28:38,118
the same object and false otherwise.

841
00:28:38,518 --> 00:28:40,188
Basically it checks whether the two

842
00:28:40,198 --> 00:28:42,188
variables point to the same object in

843
00:28:42,198 --> 00:28:42,678
memory,

844
00:28:42,998 --> 00:28:44,518
not their content.

845
00:28:45,318 --> 00:28:47,388
In this case, list one and list two are

846
00:28:47,398 --> 00:28:49,798
pointing to the same object in memory.

847
00:28:49,878 --> 00:28:52,438
So list one is list 2 is true.

848
00:28:52,678 --> 00:28:54,828
So guys, that's all we had for this video

849
00:28:54,838 --> 00:28:55,238
for you.

850
00:28:55,638 --> 00:28:57,148
Hope you liked that content.

851
00:28:57,158 --> 00:28:58,758
If you did, give it a thumbs up

852
00:28:59,598 --> 00:29:01,428
and do subscribe to our channel for more

853
00:29:01,438 --> 00:29:02,838
interesting data tech content.

854
00:29:03,158 --> 00:29:04,268
Good luck to you. Bye.

