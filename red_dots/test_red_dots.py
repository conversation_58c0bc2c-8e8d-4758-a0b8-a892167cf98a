import cv2
import numpy as np

# Load the image
image = cv2.imread('red_dots_main.png')

# Define the lower and upper bounds for the red color
lower_red = np.array([0, 0, 200])
upper_red = np.array([50, 50, 255])

# Create a mask to threshold the image and extract red pixels
mask = cv2.inRange(image, lower_red, upper_red)

# Find contours in the binary image
contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

# Extract the coordinates of the centroids of the contours
coordinates = [(int(cv2.moments(contour)["m10"] / cv2.moments(contour)["m00"]),
                int(cv2.moments(contour)["m01"] / cv2.moments(contour)["m00"]))
               for contour in contours if cv2.moments(contour)["m00"] != 0]

# Sort the coordinates based on Y-coordinate and then X-coordinate
coordinates = sorted(coordinates, key=lambda x: (x[1], x[0]))

# Print the coordinates
print(coordinates)
for i in coordinates:
    print(i)

text_list = [1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234,
             1042, 32, 45, 56, 67, 70, 232, 121, 24, 323, 33, 44, 55, 66, 77, 88, 99, 00, 12,234
             ]

# Combine coordinates with corresponding text values
result = list(zip(coordinates, text_list))
output_image = cv2.imread("base_image.png")

# Iterate over the result and put text on the image
for (x, y), text in result:
    cv2.putText(output_image, str(text), (x, y), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2, cv2.LINE_AA)

# Save the new image
cv2.imwrite('output_image.png', output_image)