1
00:00:00,240 --> 00:00:01,440
Hey there, how's it going everybody?

2
00:00:01,680 --> 00:00:03,030
In this video we'll be learning about

3
00:00:03,040 --> 00:00:05,270
Python data types, and specifically we'll

4
00:00:05,280 --> 00:00:07,360
be learning about how to work with textual data.

5
00:00:07,600 --> 00:00:09,910
And textual data in Python are represented

6
00:00:09,920 --> 00:00:10,720
with strings.

7
00:00:10,960 --> 00:00:13,670
So we currently have open our intro dot PY

8
00:00:13,680 --> 00:00:15,760
file that we were working with in the last video

9
00:00:16,120 --> 00:00:18,000
where we just printed out Hello World.

10
00:00:18,240 --> 00:00:20,150
And I'll go ahead and run this so that we

11
00:00:20,160 --> 00:00:22,080
can see that down here it does print out

12
00:00:22,320 --> 00:00:23,040
Hello World.

13
00:00:23,440 --> 00:00:25,670
Now this line here is using the print

14
00:00:25,680 --> 00:00:26,160
function

15
00:00:26,400 --> 00:00:28,590
and we're passing this text value into that

16
00:00:28,600 --> 00:00:29,280
print function.

17
00:00:29,520 --> 00:00:31,670
Now if we wanted to create a variable that

18
00:00:31,680 --> 00:00:32,800
holds that text value,

19
00:00:33,040 --> 00:00:34,400
then we could say.

20
00:00:34,480 --> 00:00:36,800
Now I'll just get rid of this comment for now.

21
00:00:37,040 --> 00:00:39,040
So if I wanted a variable to hold that value,

22
00:00:39,280 --> 00:00:40,710
then I can just create a variable and we'll

23
00:00:40,720 --> 00:00:41,680
call that message

24
00:00:41,920 --> 00:00:43,990
and we'll set that message variable equal

25
00:00:44,000 --> 00:00:46,800
to our text value that we passed in to print.

26
00:00:47,280 --> 00:00:49,600
And here message is our variable name.

27
00:00:49,840 --> 00:00:51,510
So if you're coming from another language,

28
00:00:51,520 --> 00:00:52,990
then you might be wondering if we need to

29
00:00:53,000 --> 00:00:54,990
use semicolons or something like that to

30
00:00:55,000 --> 00:00:55,920
end each line.

31
00:00:56,160 --> 00:00:57,440
But Python doesn't need that.

32
00:00:57,680 --> 00:00:59,750
It operates on whitespace alone, which

33
00:00:59,760 --> 00:01:01,840
makes it a very clean language to work with.

34
00:01:02,320 --> 00:01:03,990
Now, by convention, our variables are

35
00:01:04,000 --> 00:01:05,360
usually all lowercase,

36
00:01:05,600 --> 00:01:07,200
and if it's multiple words,

37
00:01:07,440 --> 00:01:09,430
then we separate those with under score.

38
00:01:09,440 --> 00:01:11,910
So if instead my variable name was my

39
00:01:11,920 --> 00:01:12,480
message.

40
00:01:12,720 --> 00:01:15,030
Then it would be my under score message.

41
00:01:15,040 --> 00:01:17,360
So that's just a convention that's commonly used.

42
00:01:17,760 --> 00:01:20,150
And also these variable names should be as

43
00:01:20,160 --> 00:01:22,630
descriptive as possible as to what values

44
00:01:22,640 --> 00:01:23,440
they're meant to hold.

45
00:01:23,680 --> 00:01:25,920
So message is a good variable name here

46
00:01:26,080 --> 00:01:27,920
because it's holding our message.

47
00:01:28,320 --> 00:01:30,240
But if I was instead to call this

48
00:01:30,480 --> 00:01:32,800
variable M, which is a valid variable name,

49
00:01:33,200 --> 00:01:35,790
but anyone reading my code now wouldn't

50
00:01:35,800 --> 00:01:38,150
know when they see this M variable what

51
00:01:38,160 --> 00:01:39,430
value it's supposed to hold.

52
00:01:39,440 --> 00:01:40,880
So message is a much

53
00:01:41,320 --> 00:01:42,600
better variable name in this case.

54
00:01:43,200 --> 00:01:45,680
So this message variable now holds our text data

55
00:01:45,920 --> 00:01:47,760
and our text data is called a string.

56
00:01:48,000 --> 00:01:50,230
Now we can use our variable in place of

57
00:01:50,240 --> 00:01:50,880
this string

58
00:01:51,760 --> 00:01:53,600
anywhere that we use it in our program.

59
00:01:53,760 --> 00:01:56,160
So instead of printing out hello world

60
00:01:56,400 --> 00:01:57,280
directly here,

61
00:01:57,520 --> 00:01:59,350
I'm instead going to now print out this

62
00:01:59,360 --> 00:01:59,840
variable

63
00:02:00,160 --> 00:02:01,750
and that should give us the same result.

64
00:02:01,760 --> 00:02:02,880
So if I run that

65
00:02:03,120 --> 00:02:04,670
then you can see down here we still get the

66
00:02:04,680 --> 00:02:05,280
same result.

67
00:02:05,840 --> 00:02:07,590
Now we can see that I created this string

68
00:02:07,600 --> 00:02:08,720
with single quotes.

69
00:02:08,960 --> 00:02:10,720
Now you can also use double quotes.

70
00:02:10,960 --> 00:02:12,390
So if you're wondering if there's a

71
00:02:12,400 --> 00:02:14,110
difference between the single quotes and

72
00:02:14,120 --> 00:02:14,720
the double quotes,

73
00:02:14,960 --> 00:02:17,270
then it really just depends on what text

74
00:02:17,280 --> 00:02:18,400
you have in your string.

75
00:02:18,880 --> 00:02:21,470
So for example, if you have a single quote

76
00:02:21,480 --> 00:02:22,320
in your string,

77
00:02:22,880 --> 00:02:25,230
so for example, let's create one with a

78
00:02:25,240 --> 00:02:25,910
single quote.

79
00:02:25,920 --> 00:02:27,120
Instead of Hello World,

80
00:02:27,440 --> 00:02:29,040
I will instead make our string

81
00:02:29,280 --> 00:02:30,240
Bobby's world.

82
00:02:30,880 --> 00:02:32,800
Now see, the problem here is that

83
00:02:33,280 --> 00:02:36,720
Python sees this single quote within our text

84
00:02:36,960 --> 00:02:39,110
as being the end of the string, and it's

85
00:02:39,120 --> 00:02:41,350
not going to know what to do with what

86
00:02:41,360 --> 00:02:44,160
comes after that single quote here,

87
00:02:44,480 --> 00:02:46,070
because it thinks that's where the string

88
00:02:46,080 --> 00:02:46,720
is closing.

89
00:02:46,880 --> 00:02:48,960
So if you run this now, then you'll get an error.

90
00:02:49,360 --> 00:02:50,670
Now there's a couple of different ways to

91
00:02:50,680 --> 00:02:51,280
handle this.

92
00:02:51,520 --> 00:02:52,080
We could

93
00:02:52,480 --> 00:02:55,760
escape this single quote with a back slash.

94
00:02:56,000 --> 00:02:58,870
So if I escape that single quote and now

95
00:02:58,880 --> 00:02:59,680
run this,

96
00:02:59,920 --> 00:03:02,230
now Python knows that this single quote

97
00:03:02,240 --> 00:03:03,760
doesn't closeout the string

98
00:03:04,000 --> 00:03:05,760
and that instead this one should.

99
00:03:06,160 --> 00:03:08,480
Now another way to handle that is to

100
00:03:08,560 --> 00:03:09,190
instead.

101
00:03:09,200 --> 00:03:11,120
So I'll take away that escape character.

102
00:03:11,360 --> 00:03:12,750
Now another way to handle that is to

103
00:03:12,760 --> 00:03:14,510
instead just use double quotes on the

104
00:03:14,520 --> 00:03:15,680
outside of our string,

105
00:03:16,160 --> 00:03:18,240
any string that contains single quotes.

106
00:03:18,480 --> 00:03:20,830
So if we know that our string contains a

107
00:03:20,840 --> 00:03:22,870
single quote, then we can instead use

108
00:03:22,880 --> 00:03:24,320
double quotes on the outside

109
00:03:24,560 --> 00:03:26,790
and then it'll know that that single quote

110
00:03:26,800 --> 00:03:27,920
isn't the end of the string.

111
00:03:28,160 --> 00:03:30,150
So now if we run this, then we can see that

112
00:03:30,160 --> 00:03:31,120
it still works fine.

113
00:03:31,400 --> 00:03:32,670
But that doesn't necessarily mean that

114
00:03:32,680 --> 00:03:34,310
double quotes are better because it goes

115
00:03:34,320 --> 00:03:34,960
both ways.

116
00:03:35,120 --> 00:03:36,950
If your string contains double quotes in

117
00:03:36,960 --> 00:03:37,440
the message,

118
00:03:37,680 --> 00:03:39,750
then I would use single quotes on the

119
00:03:39,760 --> 00:03:40,240
outside.

120
00:03:40,640 --> 00:03:42,790
Now if we needed to create a multi line

121
00:03:42,800 --> 00:03:43,280
string,

122
00:03:43,520 --> 00:03:45,670
then one way we can do this is by using

123
00:03:45,680 --> 00:03:47,830
three quotes at the beginning and end of

124
00:03:47,840 --> 00:03:48,480
our string.

125
00:03:48,720 --> 00:03:51,270
And these can also be single or double

126
00:03:51,280 --> 00:03:52,080
quotes as well.

127
00:03:52,320 --> 00:03:54,080
So let's go ahead and look at an example.

128
00:03:54,240 --> 00:03:57,070
So I'll add 3 quotes to the beginning and

129
00:03:57,080 --> 00:03:58,880
end of our string here.

130
00:04:00,440 --> 00:04:01,910
And now I'll just add some text to span

131
00:04:01,920 --> 00:04:02,870
some multiple lines here.

132
00:04:02,880 --> 00:04:03,760
So I'll just say

133
00:04:04,000 --> 00:04:06,230
was a good and then hit enter to go to a

134
00:04:06,240 --> 00:04:06,800
new line

135
00:04:07,040 --> 00:04:07,520
and say

136
00:04:07,840 --> 00:04:09,080
cartoon in the

137
00:04:09,320 --> 00:04:10,400
1990s.

138
00:04:10,800 --> 00:04:12,640
So now if we run that,

139
00:04:12,880 --> 00:04:14,350
then we can see that it printed out our

140
00:04:14,360 --> 00:04:16,720
string correctly over multiple lines.

141
00:04:17,280 --> 00:04:19,120
OK, so let's go back to

142
00:04:19,440 --> 00:04:21,750
our simple Hello World example.

143
00:04:21,760 --> 00:04:23,360
So I'm just going to take all of that

144
00:04:23,600 --> 00:04:24,880
and replace it with

145
00:04:25,120 --> 00:04:27,120
our previous Hello World example.

146
00:04:27,440 --> 00:04:29,190
And now let's go ahead and just run that

147
00:04:29,200 --> 00:04:29,680
really quick.

148
00:04:29,760 --> 00:04:31,280
So we're back to our starting point.

149
00:04:31,600 --> 00:04:33,680
So we can think of our string as a string

150
00:04:33,720 --> 00:04:34,880
of individual characters,

151
00:04:35,120 --> 00:04:36,550
and we can access these individual

152
00:04:36,560 --> 00:04:37,440
characters also.

153
00:04:37,680 --> 00:04:40,310
So first, let's see how we can find how

154
00:04:40,320 --> 00:04:42,000
many characters are in our string.

155
00:04:42,160 --> 00:04:43,560
So to do this, we can use the

156
00:04:43,840 --> 00:04:46,400
LEN function, which stands for length.

157
00:04:46,640 --> 00:04:47,120
So

158
00:04:47,440 --> 00:04:49,520
whenever I print out here, if I was instead

159
00:04:49,640 --> 00:04:50,720
of printing my message,

160
00:04:50,960 --> 00:04:52,000
if I was to print out

161
00:04:52,320 --> 00:04:52,720
this

162
00:04:53,040 --> 00:04:55,670
length function and pass in message and

163
00:04:55,680 --> 00:04:56,560
then run this.

164
00:04:56,960 --> 00:04:58,310
Now we're no longer printing out our

165
00:04:58,320 --> 00:05:00,550
message, we're printing out the length of

166
00:05:00,560 --> 00:05:01,040
our message,

167
00:05:01,320 --> 00:05:03,120
and we can see that it says that the length

168
00:05:03,200 --> 00:05:04,880
of our string is 11.

169
00:05:05,200 --> 00:05:10,560
And if we counted these up 1234567891011,

170
00:05:10,720 --> 00:05:12,000
then we can see that that's correct

171
00:05:12,320 --> 00:05:14,550
and we can access our string's characters

172
00:05:14,560 --> 00:05:15,280
individually.

173
00:05:15,520 --> 00:05:18,310
So to do this we can use the square

174
00:05:18,320 --> 00:05:19,120
brackets

175
00:05:19,600 --> 00:05:22,350
after our string and pass in the location

176
00:05:22,360 --> 00:05:23,680
of the character that we want.

177
00:05:23,920 --> 00:05:25,360
So I'll say print

178
00:05:25,600 --> 00:05:28,080
message and then square brackets.

179
00:05:28,400 --> 00:05:30,750
Now the location is called an index and it

180
00:05:30,760 --> 00:05:32,000
starts out as zero.

181
00:05:32,240 --> 00:05:34,390
So to access the first character of our

182
00:05:34,400 --> 00:05:35,920
string we can say

183
00:05:36,480 --> 00:05:37,520
print the message

184
00:05:37,760 --> 00:05:39,040
and then access this.

185
00:05:39,280 --> 00:05:40,080
Index of

186
00:05:40,480 --> 00:05:40,550
0.

187
00:05:40,560 --> 00:05:41,920
So if we print that,

188
00:05:42,200 --> 00:05:44,880
then we can see that we got the capital H

189
00:05:45,200 --> 00:05:45,360
Now

190
00:05:45,600 --> 00:05:47,760
since the length of our string is 11,

191
00:05:48,000 --> 00:05:50,110
that means that with the first character

192
00:05:50,120 --> 00:05:50,880
starting out at

193
00:05:51,200 --> 00:05:51,440
0,

194
00:05:51,680 --> 00:05:54,000
our last character would be at index 10.

195
00:05:54,240 --> 00:05:56,880
So it's our total length -1.

196
00:05:57,040 --> 00:05:58,880
So if I was to say print out

197
00:05:59,120 --> 00:06:02,080
the location at index 10, the value at

198
00:06:02,320 --> 00:06:03,120
index 10,

199
00:06:03,440 --> 00:06:05,430
then we can see that we got our D

200
00:06:05,440 --> 00:06:07,510
character, which is the last character in

201
00:06:07,520 --> 00:06:08,000
that string.

202
00:06:08,400 --> 00:06:10,150
Now, if you accidentally try to access an

203
00:06:10,160 --> 00:06:12,190
index that doesn't exist, then you'll get

204
00:06:12,200 --> 00:06:13,030
an index error.

205
00:06:13,040 --> 00:06:14,000
So if we were to say

206
00:06:14,720 --> 00:06:15,720
access the

207
00:06:16,000 --> 00:06:17,120
index of 11,

208
00:06:17,400 --> 00:06:19,600
then we can see that that threw an index error.

209
00:06:19,920 --> 00:06:21,990
Now we can also access a range of

210
00:06:22,000 --> 00:06:22,560
characters.

211
00:06:22,720 --> 00:06:25,270
So if I just wanted to get the word hello

212
00:06:25,280 --> 00:06:26,080
from our string,

213
00:06:26,400 --> 00:06:28,160
then we could say

214
00:06:28,400 --> 00:06:29,200
that we want

215
00:06:30,000 --> 00:06:32,830
zero and then this colon 5, and we'll

216
00:06:32,840 --> 00:06:33,680
explain this.

217
00:06:34,000 --> 00:06:36,560
So the first index here is our starting point.

218
00:06:36,800 --> 00:06:39,350
And the second index, which is separated by

219
00:06:39,360 --> 00:06:41,600
this colon, is the stopping point.

220
00:06:41,920 --> 00:06:43,840
Now, one thing a little confusing about this

221
00:06:44,080 --> 00:06:46,790
is that the first index is inclusive, which

222
00:06:46,800 --> 00:06:48,800
means it's going to include that value,

223
00:06:49,040 --> 00:06:50,800
but the second index is not.

224
00:06:50,960 --> 00:06:52,550
Now, there's good reasons for this, but

225
00:06:52,560 --> 00:06:54,000
it's still easy to forget.

226
00:06:54,240 --> 00:06:54,560
So

227
00:06:54,960 --> 00:06:56,950
basically what we're saying is I want all

228
00:06:56,960 --> 00:06:57,680
the characters

229
00:06:58,240 --> 00:07:00,990
between the beginning and up to but not

230
00:07:01,000 --> 00:07:01,880
including the

231
00:07:02,160 --> 00:07:03,160
5th index, and

232
00:07:03,440 --> 00:07:05,350
it'll be more clear if we just go ahead and

233
00:07:05,360 --> 00:07:05,840
run this.

234
00:07:06,080 --> 00:07:07,600
So we can see that it prints out

235
00:07:08,160 --> 00:07:08,720
hello.

236
00:07:08,960 --> 00:07:11,670
So it printed out our message from the zero

237
00:07:11,680 --> 00:07:12,480
index here

238
00:07:12,800 --> 00:07:15,750
all the way up to but not including the 5th

239
00:07:15,760 --> 00:07:16,710
index here.

240
00:07:16,720 --> 00:07:17,760
So we got hello.

241
00:07:18,160 --> 00:07:20,470
Now since our starting point here is just

242
00:07:20,480 --> 00:07:22,950
the first index, we can actually just leave

243
00:07:22,960 --> 00:07:23,520
that off

244
00:07:23,760 --> 00:07:25,590
and it'll assume that we want to start at

245
00:07:25,600 --> 00:07:26,160
the beginning.

246
00:07:26,400 --> 00:07:28,470
So if we don't put anything there and then

247
00:07:28,480 --> 00:07:29,600
colon 5,

248
00:07:29,840 --> 00:07:31,670
then we should get the same thing.

249
00:07:31,680 --> 00:07:34,000
So if we run that we can see we still got hello.

250
00:07:34,400 --> 00:07:36,880
Now instead, if we wanted to grab the word

251
00:07:36,960 --> 00:07:38,320
world from the string,

252
00:07:38,640 --> 00:07:40,000
then we could start

253
00:07:40,240 --> 00:07:42,080
at the 6th index

254
00:07:42,400 --> 00:07:44,880
and then we can just go to the end.

255
00:07:45,080 --> 00:07:47,270
And just like leaving off our starting

256
00:07:47,280 --> 00:07:49,360
index, it will start from the beginning.

257
00:07:49,600 --> 00:07:51,830
Leaving off the stop index just goes all

258
00:07:51,840 --> 00:07:52,720
the way to the end.

259
00:07:52,880 --> 00:07:55,350
So now if we run that then we can see that

260
00:07:55,360 --> 00:07:57,360
it gives us back the word world.

261
00:07:57,760 --> 00:07:59,270
Now, what we're doing here is called

262
00:07:59,280 --> 00:08:01,270
slicing, and if you'd like to learn more

263
00:08:01,280 --> 00:08:03,350
about slicing in depth, then you can watch

264
00:08:03,360 --> 00:08:05,390
my detailed video on that, and I'll leave a

265
00:08:05,400 --> 00:08:07,360
link to that in the description section below.

266
00:08:07,840 --> 00:08:09,910
So now let's just go back to printing out

267
00:08:09,920 --> 00:08:12,320
our message and let me run this.

268
00:08:12,720 --> 00:08:14,390
OK, so all of the data types that we're

269
00:08:14,400 --> 00:08:16,230
going to review are going to have certain

270
00:08:16,240 --> 00:08:18,710
methods available to us that give us access

271
00:08:18,720 --> 00:08:20,400
to a lot of useful functionality.

272
00:08:20,560 --> 00:08:22,000
Now, when I say methods,

273
00:08:22,240 --> 00:08:23,550
a lot of people wonder what's the

274
00:08:23,560 --> 00:08:26,000
difference between a method and a function.

275
00:08:26,320 --> 00:08:28,550
And functions and methods are basically the

276
00:08:28,560 --> 00:08:29,280
same thing.

277
00:08:29,480 --> 00:08:31,870
A method is just a function that belongs to

278
00:08:31,880 --> 00:08:32,520
an object.

279
00:08:32,720 --> 00:08:34,390
It's not important to get into the details

280
00:08:34,400 --> 00:08:35,120
of that now,

281
00:08:35,360 --> 00:08:37,760
but if you hear me say method or function,

282
00:08:37,920 --> 00:08:39,590
then you can basically think of those as

283
00:08:39,600 --> 00:08:40,720
the same thing for now.

284
00:08:41,200 --> 00:08:43,030
So like I was saying, the data types that

285
00:08:43,040 --> 00:08:44,000
we'll be going over

286
00:08:44,240 --> 00:08:46,320
all have certain methods available to us

287
00:08:46,560 --> 00:08:48,630
that give us access to a lot of useful

288
00:08:48,640 --> 00:08:49,360
functionality.

289
00:08:49,520 --> 00:08:51,670
So let's look at some of these string

290
00:08:51,680 --> 00:08:52,160
methods.

291
00:08:52,400 --> 00:08:55,280
So we can see here that our hello world text

292
00:08:55,520 --> 00:08:56,800
is capitalized,

293
00:08:57,040 --> 00:08:58,400
but let's say that we wanted

294
00:08:58,800 --> 00:09:00,320
that to be all lowercase.

295
00:09:00,560 --> 00:09:03,120
Now to do this, it's just as easy as saying

296
00:09:03,440 --> 00:09:04,800
print message

297
00:09:05,040 --> 00:09:07,360
and then to lowercase this we can say

298
00:09:07,600 --> 00:09:08,720
dot lower.

299
00:09:09,520 --> 00:09:11,910
Now when we run this dot lower with these

300
00:09:11,920 --> 00:09:12,720
parentheses here,

301
00:09:12,960 --> 00:09:14,990
that's running the lower method on the

302
00:09:15,000 --> 00:09:15,440
string.

303
00:09:15,840 --> 00:09:18,070
So when we ran that, we can see that now

304
00:09:18,080 --> 00:09:19,950
our hello world has been set to all

305
00:09:19,960 --> 00:09:20,720
lowercase.

306
00:09:20,960 --> 00:09:22,800
And if we wanted to do this to uppercase,

307
00:09:22,960 --> 00:09:26,480
then it's as easy as changing that lower to upper.

308
00:09:26,520 --> 00:09:27,280
If we run that,

309
00:09:27,520 --> 00:09:29,190
now we can see that hello world is all

310
00:09:29,200 --> 00:09:29,840
uppercase.

311
00:09:30,240 --> 00:09:31,750
So now let's say that we want to count a

312
00:09:31,760 --> 00:09:33,680
certain number of characters in our string.

313
00:09:33,880 --> 00:09:35,880
And to do that we can use the count method. And

314
00:09:36,160 --> 00:09:38,720
the count method actually takes a string as

315
00:09:38,760 --> 00:09:39,440
an argument.

316
00:09:39,600 --> 00:09:40,880
So we can say

317
00:09:41,200 --> 00:09:43,040
message dot count

318
00:09:43,280 --> 00:09:45,360
and now we have to pass in an argument.

319
00:09:45,640 --> 00:09:47,910
And it has to know what we want to count in

320
00:09:47,920 --> 00:09:48,400
our message.

321
00:09:48,640 --> 00:09:51,030
So for now we'll just say count hello.

322
00:09:51,040 --> 00:09:51,920
And if we run that,

323
00:09:52,160 --> 00:09:54,070
we can see that it returns that the string

324
00:09:54,080 --> 00:09:56,880
hello appears in our message one time.

325
00:09:57,120 --> 00:09:59,510
But if we instead just passed in a single

326
00:09:59,520 --> 00:10:02,160
character as our argument, so I'll pass in an L

327
00:10:02,400 --> 00:10:03,280
If I run that,

328
00:10:03,520 --> 00:10:04,640
you can see that we get

329
00:10:04,880 --> 00:10:07,190
a three because there are three LS in our

330
00:10:07,200 --> 00:10:07,920
message variable.

331
00:10:08,400 --> 00:10:10,430
So if we instead want to find the index of

332
00:10:10,440 --> 00:10:12,000
where certain characters can be found,

333
00:10:12,240 --> 00:10:14,160
then we can use the find method.

334
00:10:14,400 --> 00:10:15,910
So I could come up here and instead of

335
00:10:15,920 --> 00:10:18,400
saying dot count, I can say dot find.

336
00:10:18,640 --> 00:10:20,550
And now this takes an argument as well.

337
00:10:20,560 --> 00:10:22,080
It's what we want to find.

338
00:10:22,240 --> 00:10:25,360
So let me type in world here and run this

339
00:10:25,680 --> 00:10:27,390
and we can see when we run this that it

340
00:10:27,400 --> 00:10:28,560
returns a six

341
00:10:28,880 --> 00:10:31,520
and that's because world starts at the 6th

342
00:10:31,600 --> 00:10:33,600
index of our message variable.

343
00:10:34,000 --> 00:10:35,750
Now if we try to find a string of

344
00:10:35,760 --> 00:10:37,200
characters that doesn't exist.

345
00:10:37,638 --> 00:10:38,918
Then it will just return

346
00:10:39,238 --> 00:10:39,548
-1.

347
00:10:39,558 --> 00:10:42,588
So if instead of world I typed in universe

348
00:10:42,598 --> 00:10:43,318
and ran that,

349
00:10:43,638 --> 00:10:45,868
you can see it returns a -1 because it

350
00:10:45,878 --> 00:10:47,868
can't find that anywhere in our message

351
00:10:47,878 --> 00:10:48,278
variable.

352
00:10:48,758 --> 00:10:50,068
OK, so now let's say that we want to

353
00:10:50,078 --> 00:10:52,428
replace some characters in our string with

354
00:10:52,438 --> 00:10:53,318
some other characters,

355
00:10:53,558 --> 00:10:55,718
and we can do this with the replace method.

356
00:10:55,958 --> 00:10:57,868
Now first I'm going to change this, change

357
00:10:57,878 --> 00:10:59,958
this back to printing out our

358
00:11:00,278 --> 00:11:03,718
regular message, so I'll just delete these.

359
00:11:04,198 --> 00:11:05,708
Now let's try to use our replace method

360
00:11:05,718 --> 00:11:07,948
right below where we first set our message

361
00:11:07,958 --> 00:11:08,518
variable.

362
00:11:08,678 --> 00:11:10,838
And this method takes 2 arguments.

363
00:11:11,318 --> 00:11:13,398
First it takes what we want to replace.

364
00:11:13,558 --> 00:11:16,118
So first let me just say message dot

365
00:11:16,438 --> 00:11:17,158
replace.

366
00:11:17,638 --> 00:11:19,748
So first it takes what we want to replace.

367
00:11:19,758 --> 00:11:21,558
So let's say that we want to replace

368
00:11:22,518 --> 00:11:23,158
world.

369
00:11:23,398 --> 00:11:25,348
And now the second argument, which is

370
00:11:25,358 --> 00:11:26,758
separated by a comma,

371
00:11:26,998 --> 00:11:29,158
is what we want to replace world with.

372
00:11:29,318 --> 00:11:31,798
So we'll replace world with universe.

373
00:11:32,198 --> 00:11:33,718
So now if we run this,

374
00:11:33,958 --> 00:11:35,878
then this might be a little unexpected.

375
00:11:36,038 --> 00:11:37,788
We can see that it's still printing out

376
00:11:37,798 --> 00:11:38,678
hello world.

377
00:11:38,918 --> 00:11:41,478
Now the reason our replacement didn't work

378
00:11:41,638 --> 00:11:43,708
is because it's not making that replacement

379
00:11:43,718 --> 00:11:44,438
in place.

380
00:11:44,678 --> 00:11:46,988
It's actually returning a new string with

381
00:11:46,998 --> 00:11:48,278
those values replaced.

382
00:11:48,518 --> 00:11:50,108
And we'll learn more about return

383
00:11:50,118 --> 00:11:52,038
statements when we learn about functions.

384
00:11:52,278 --> 00:11:53,158
But basically

385
00:11:53,398 --> 00:11:55,238
we need to set a new variable here

386
00:11:55,798 --> 00:11:57,948
to get that returned string with those

387
00:11:57,958 --> 00:11:58,598
replacements.

388
00:11:58,838 --> 00:12:02,078
So I could say something like new message.

389
00:12:02,758 --> 00:12:03,878
is equal to

390
00:12:04,678 --> 00:12:06,358
that original method with

391
00:12:06,598 --> 00:12:07,678
this replacement.

392
00:12:08,038 --> 00:12:11,108
So now if we set that new variable and

393
00:12:11,118 --> 00:12:13,638
instead print that new message and run that,

394
00:12:13,958 --> 00:12:16,108
then you can see that now it replaced world

395
00:12:16,118 --> 00:12:17,078
with universe.

396
00:12:17,318 --> 00:12:19,148
And if you really wanted to make the

397
00:12:19,158 --> 00:12:20,918
replacement to the message variable,

398
00:12:21,158 --> 00:12:22,948
then instead of making a new message

399
00:12:22,958 --> 00:12:23,398
variable,

400
00:12:23,638 --> 00:12:26,588
then we can just set this same message

401
00:12:26,598 --> 00:12:27,718
variable again.

402
00:12:28,238 --> 00:12:29,948
So now we're setting the same message

403
00:12:29,958 --> 00:12:32,588
variable equal to that replacement and then

404
00:12:32,598 --> 00:12:33,318
printing it out.

405
00:12:33,398 --> 00:12:34,518
So if I run that,

406
00:12:34,918 --> 00:12:36,308
then we can see that now the message

407
00:12:36,318 --> 00:12:38,838
variable had world replaced with universe.

408
00:12:38,998 --> 00:12:40,908
And this may look a little strange to set a

409
00:12:40,918 --> 00:12:41,878
variable to

410
00:12:42,118 --> 00:12:44,348
an altered version of itself, but it's very

411
00:12:44,358 --> 00:12:46,118
common and you'll be using that a lot.

412
00:12:46,638 --> 00:12:49,228
OK, so now let's get rid of this replace

413
00:12:49,238 --> 00:12:50,358
line here,

414
00:12:50,638 --> 00:12:52,278
and now let's look at how we can add

415
00:12:52,358 --> 00:12:54,388
multiple strings and concatenate them

416
00:12:54,398 --> 00:12:54,918
together.

417
00:12:55,198 --> 00:12:56,838
So instead of saying hello world,

418
00:12:57,158 --> 00:13:00,038
I'm instead just going to set this equal to hello

419
00:13:00,358 --> 00:13:02,908
and instead of calling this message, I'm

420
00:13:02,918 --> 00:13:04,918
going to set this equal to greeting.

421
00:13:05,238 --> 00:13:06,828
And just below greeting, I'm going to

422
00:13:06,838 --> 00:13:08,918
create a variable called name and

423
00:13:09,158 --> 00:13:10,398
I'm going to set that equal to,

424
00:13:10,838 --> 00:13:12,358
we'll just say Michael.

425
00:13:12,718 --> 00:13:16,198
And now lastly, let's create a message here

426
00:13:16,598 --> 00:13:17,078
and

427
00:13:17,318 --> 00:13:19,948
we want this message to combine our

428
00:13:19,958 --> 00:13:21,398
greeting with our name.

429
00:13:21,558 --> 00:13:23,638
We want it to say hello Michael.

430
00:13:24,118 --> 00:13:26,308
And one way to do this is to use the plus

431
00:13:26,318 --> 00:13:28,038
sign operator. So

432
00:13:28,278 --> 00:13:31,958
we could try this by saying greeting plus name.

433
00:13:32,198 --> 00:13:33,878
Now if we run this

434
00:13:34,238 --> 00:13:36,268
and we can see that it's not exactly what

435
00:13:36,278 --> 00:13:36,748
we wanted.

436
00:13:36,758 --> 00:13:38,198
It combined them together,

437
00:13:38,438 --> 00:13:40,438
but it doesn't have a space there.

438
00:13:40,718 --> 00:13:42,348
So when you're concatenating strings

439
00:13:42,358 --> 00:13:42,838
together,

440
00:13:43,078 --> 00:13:44,998
it's easy to make mistakes like this.

441
00:13:45,238 --> 00:13:48,358
So what we wanna do is add a string between them

442
00:13:48,758 --> 00:13:50,038
that spaces them out.

443
00:13:50,278 --> 00:13:52,518
So we can add a string between these

444
00:13:52,758 --> 00:13:53,558
just by

445
00:13:53,798 --> 00:13:54,278
putting in

446
00:13:54,518 --> 00:13:55,878
a string literal here.

447
00:13:56,118 --> 00:13:58,108
And I'm also gonna put in a comma and a

448
00:13:58,118 --> 00:13:58,838
space to

449
00:13:59,238 --> 00:14:00,038
separate those.

450
00:14:00,318 --> 00:14:03,148
And now we also need another plus sign so

451
00:14:03,158 --> 00:14:05,148
that we're adding the name after that

452
00:14:05,158 --> 00:14:05,638
string.

453
00:14:05,918 --> 00:14:07,388
So now we're saying that we want this

454
00:14:07,398 --> 00:14:08,838
greeting which is hello

455
00:14:09,398 --> 00:14:11,878
plus this comma and space

456
00:14:12,198 --> 00:14:13,308
plus the name.

457
00:14:13,318 --> 00:14:14,438
So if I run this

458
00:14:14,758 --> 00:14:16,468
then we can see that it concatenated those

459
00:14:16,478 --> 00:14:18,038
strings together how we wanted.

460
00:14:18,278 --> 00:14:20,588
Now sometimes using the plus sign isn't the

461
00:14:20,598 --> 00:14:21,638
best way to go.

462
00:14:21,918 --> 00:14:23,628
If we wanted to create a longer, more

463
00:14:23,638 --> 00:14:24,838
complicated string

464
00:14:25,078 --> 00:14:26,678
while using our variables,

465
00:14:26,918 --> 00:14:28,998
then adding them all together like this

466
00:14:29,158 --> 00:14:30,678
might get hard to keep track of.

467
00:14:30,838 --> 00:14:32,638
So let's say that we wanted to add to the

468
00:14:32,678 --> 00:14:33,558
end of our message

469
00:14:34,198 --> 00:14:34,838
just by,

470
00:14:35,158 --> 00:14:37,148
you know, closing off a sentence and saying

471
00:14:37,158 --> 00:14:37,718
welcome.

472
00:14:37,878 --> 00:14:40,428
So to do that, after our name variable, we

473
00:14:40,438 --> 00:14:43,468
could add another string that is a period

474
00:14:43,478 --> 00:14:44,758
to close off that sentence,

475
00:14:44,998 --> 00:14:45,718
a space,

476
00:14:45,958 --> 00:14:48,398
and then welcome with an exclamation point.

477
00:14:48,518 --> 00:14:49,598
So let's go ahead and run that.

478
00:14:50,238 --> 00:14:51,548
Now we can see that it printed out the

479
00:14:51,558 --> 00:14:53,238
string how we wanted it to look,

480
00:14:53,558 --> 00:14:54,988
but it's starting to get a little

481
00:14:54,998 --> 00:14:57,878
complicated on this line here to keep track

482
00:14:57,918 --> 00:14:59,958
of all of our plus signs and spaces

483
00:15:00,198 --> 00:15:01,318
within our message.

484
00:15:01,878 --> 00:15:03,558
Instead, with strings like this,

485
00:15:03,798 --> 00:15:05,828
it's usually better to use a formatted

486
00:15:05,838 --> 00:15:06,198
string.

487
00:15:06,678 --> 00:15:08,988
This allows us to write the sentence as it

488
00:15:08,998 --> 00:15:09,638
will appear

489
00:15:09,878 --> 00:15:11,708
and put placeholders in place of our

490
00:15:11,718 --> 00:15:12,198
variables.

491
00:15:12,358 --> 00:15:14,148
So let's go ahead and see what this would

492
00:15:14,158 --> 00:15:14,678
look like.

493
00:15:14,998 --> 00:15:17,308
So to instead use a formatted string, we

494
00:15:17,318 --> 00:15:18,598
can say message

495
00:15:18,918 --> 00:15:19,958
is equal to.

496
00:15:20,238 --> 00:15:21,868
And we're just going to write it exactly

497
00:15:21,878 --> 00:15:22,678
how it appears,

498
00:15:22,918 --> 00:15:25,708
except everywhere where we want to replace

499
00:15:25,718 --> 00:15:26,428
with a variable.

500
00:15:26,438 --> 00:15:28,428
We're going to put in a placeholder, and

501
00:15:28,438 --> 00:15:30,188
those placeholders are going to be these

502
00:15:30,198 --> 00:15:31,078
curly brackets.

503
00:15:31,238 --> 00:15:33,398
So we want a placeholder for the greeting,

504
00:15:33,638 --> 00:15:35,718
and then a comma, a space,

505
00:15:36,038 --> 00:15:37,558
then a placeholder for the name,

506
00:15:37,798 --> 00:15:38,598
then a period,

507
00:15:38,918 --> 00:15:41,078
and then we'll type out welcome

508
00:15:41,478 --> 00:15:42,798
and an exclamation point.

509
00:15:43,118 --> 00:15:45,308
And now to fill those placeholders with our

510
00:15:45,318 --> 00:15:48,198
variables, we can say dot format.

511
00:15:48,638 --> 00:15:51,148
And pass in greeting for the first

512
00:15:51,158 --> 00:15:51,798
placeholder

513
00:15:52,038 --> 00:15:54,278
and then name for the second placeholder.

514
00:15:54,518 --> 00:15:56,588
So now let's go ahead and delete this line

515
00:15:56,598 --> 00:15:59,078
where we were using the plus sign

516
00:15:59,558 --> 00:16:00,108
operator.

517
00:16:00,118 --> 00:16:01,238
So if we run this,

518
00:16:01,478 --> 00:16:03,388
we can see that we got the same thing as

519
00:16:03,398 --> 00:16:04,998
when we concatenated them together.

520
00:16:05,238 --> 00:16:07,398
But with longer, more complicated strings,

521
00:16:07,558 --> 00:16:09,548
this is actually a little bit easier to read.

522
00:16:09,558 --> 00:16:11,668
It might look a little confusing now since

523
00:16:11,678 --> 00:16:12,988
we're just seeing these placeholders for

524
00:16:12,998 --> 00:16:13,718
the first time.

525
00:16:13,998 --> 00:16:15,628
But after you get used to this, you will

526
00:16:15,638 --> 00:16:17,788
realize that this is easier than keeping

527
00:16:17,798 --> 00:16:19,398
track of all those concatenations.

528
00:16:19,798 --> 00:16:21,718
And using string formatting like this also

529
00:16:21,798 --> 00:16:23,398
gives us some extra functionality.

530
00:16:23,638 --> 00:16:24,908
And if you want to see everything that you

531
00:16:24,918 --> 00:16:25,558
can do with that,

532
00:16:25,798 --> 00:16:28,028
then I do have a detailed in-depth video on

533
00:16:28,038 --> 00:16:29,948
string formatting, and I'll add that to the

534
00:16:29,958 --> 00:16:31,078
description section below.

535
00:16:31,558 --> 00:16:34,988
OK, and lastly, if you're using Python 3.6

536
00:16:34,998 --> 00:16:35,878
or above,

537
00:16:36,118 --> 00:16:38,188
then you'll have access to these new things

538
00:16:38,198 --> 00:16:39,318
called fstrings.

539
00:16:39,518 --> 00:16:42,438
Now, if you aren't using 3.6 or higher,

540
00:16:42,598 --> 00:16:44,348
then these aren't going to work because

541
00:16:44,358 --> 00:16:45,958
they were only recently released.

542
00:16:46,198 --> 00:16:48,108
And not a lot of people are using these

543
00:16:48,118 --> 00:16:50,918
F-strings yet, but I'm really liking them so far.

544
00:16:51,198 --> 00:16:53,678
Basically, the idea behind F-strings was to

545
00:16:53,718 --> 00:16:55,748
make string formatting as simple as

546
00:16:55,758 --> 00:16:56,278
possible.

547
00:16:56,438 --> 00:16:58,118
So let's go ahead and see what these look like.

548
00:16:58,518 --> 00:17:00,508
So to say that we want this to be an

549
00:17:00,518 --> 00:17:01,158
F-string,

550
00:17:01,558 --> 00:17:04,148
we can just add an F right here to the

551
00:17:04,158 --> 00:17:06,598
beginning on the outside of the string.

552
00:17:07,118 --> 00:17:08,118
Now, instead of using this

553
00:17:08,358 --> 00:17:09,238
.format method,

554
00:17:09,478 --> 00:17:11,628
we'll instead just write the variables

555
00:17:11,638 --> 00:17:13,558
directly within the placeholders.

556
00:17:13,718 --> 00:17:15,798
So I can remove that .format method, and

557
00:17:16,118 --> 00:17:17,598
now we can pass the

558
00:17:18,278 --> 00:17:20,588
variables directly within the placeholder.

559
00:17:20,598 --> 00:17:22,788
So we'll pass greeting to that placeholder

560
00:17:22,798 --> 00:17:23,878
and name to that one.

561
00:17:24,118 --> 00:17:25,798
So I can save that and run it.

562
00:17:26,158 --> 00:17:27,628
And you can see that using that, we got the

563
00:17:27,638 --> 00:17:28,438
same result.

564
00:17:28,678 --> 00:17:30,988
And like I said, these fstrings are pretty

565
00:17:30,998 --> 00:17:33,388
new to the language, so even people who are

566
00:17:33,398 --> 00:17:35,468
very familiar with Python may be seeing

567
00:17:35,478 --> 00:17:36,598
this for the first time.

568
00:17:36,918 --> 00:17:38,438
Now, one reason I really like these

569
00:17:38,518 --> 00:17:38,998
fstrings

570
00:17:39,238 --> 00:17:41,148
is because you can actually write code

571
00:17:41,158 --> 00:17:42,438
within the placeholder.

572
00:17:42,598 --> 00:17:44,748
So if I wanted the name in all caps for

573
00:17:44,758 --> 00:17:47,118
some reason, then I could just come into

574
00:17:47,158 --> 00:17:50,278
the placeholder here and say name.upper

575
00:17:50,518 --> 00:17:52,348
and run that directly within the

576
00:17:52,358 --> 00:17:52,998
placeholder.

577
00:17:53,238 --> 00:17:54,038
If I run that,

578
00:17:54,278 --> 00:17:56,428
then you can see that now our name was

579
00:17:56,438 --> 00:17:57,158
capitalized.

580
00:17:57,638 --> 00:17:59,548
Okay, so we're almost finished up, but I

581
00:17:59,558 --> 00:18:01,548
wanted to show you one last trick here when

582
00:18:01,558 --> 00:18:03,238
you're learning something new in Python.

583
00:18:03,518 --> 00:18:05,148
So we saw a lot of different methods that

584
00:18:05,158 --> 00:18:06,678
we could use on our strings.

585
00:18:06,918 --> 00:18:08,908
Now, if we ever wanted to see everything

586
00:18:08,918 --> 00:18:10,508
that's available to us, then there are a

587
00:18:10,518 --> 00:18:12,198
couple of things that we can do.

588
00:18:12,598 --> 00:18:16,068
So first we can use this dir function, and

589
00:18:16,078 --> 00:18:17,718
that looks like this.

590
00:18:18,118 --> 00:18:18,478
And

591
00:18:18,758 --> 00:18:21,038
what this does is if we pass in a

592
00:18:21,318 --> 00:18:21,878
variable,

593
00:18:22,118 --> 00:18:24,788
then it will show us all of the attributes

594
00:18:24,798 --> 00:18:27,188
and methods that we have access to with

595
00:18:27,198 --> 00:18:27,958
that variable.

596
00:18:28,438 --> 00:18:30,428
Now don't worry about all of these double

597
00:18:30,438 --> 00:18:31,798
under score methods yet,

598
00:18:32,118 --> 00:18:35,148
but if we look down here then we'll see a

599
00:18:35,158 --> 00:18:37,188
couple of familiar methods that we used in

600
00:18:37,198 --> 00:18:37,878
this video.

601
00:18:38,518 --> 00:18:42,198
So for example, we have dot upper here, we have

602
00:18:42,918 --> 00:18:44,118
dot replace

603
00:18:44,958 --> 00:18:45,958
dot lower.

604
00:18:46,278 --> 00:18:48,428
So this kind of gives you a broad overview

605
00:18:48,438 --> 00:18:50,508
of all the attributes and methods that are

606
00:18:50,518 --> 00:18:51,318
available to you.

607
00:18:51,758 --> 00:18:53,548
Now this doesn't show what any of these

608
00:18:53,558 --> 00:18:54,438
actually do.

609
00:18:54,598 --> 00:18:56,828
So to see more information about these

610
00:18:56,838 --> 00:18:57,718
string methods,

611
00:18:57,958 --> 00:19:00,118
then we can use the help function.

612
00:19:00,438 --> 00:19:03,958
But if we run this help function on the name,

613
00:19:04,198 --> 00:19:05,068
then that won't work.

614
00:19:05,078 --> 00:19:07,788
We actually need to run it on the string

615
00:19:07,798 --> 00:19:08,918
class itself.

616
00:19:09,158 --> 00:19:12,508
So we'll just type in help and then STR for

617
00:19:12,518 --> 00:19:13,068
string.

618
00:19:13,078 --> 00:19:14,118
And if I run that,

619
00:19:14,358 --> 00:19:16,348
then we can see that this gives us a lot

620
00:19:16,358 --> 00:19:18,918
more information and I'll pull this up here a bit.

621
00:19:19,638 --> 00:19:20,678
So it,

622
00:19:21,078 --> 00:19:22,548
you know, tells us everything that's

623
00:19:22,558 --> 00:19:23,318
available to us.

624
00:19:23,558 --> 00:19:26,038
And if I Scroll down here a bit

625
00:19:26,358 --> 00:19:28,028
and if I try to find something that we

626
00:19:28,038 --> 00:19:30,108
actually used in this video, OK, so we have

627
00:19:30,118 --> 00:19:30,998
find here.

628
00:19:31,358 --> 00:19:33,028
So you can see that it actually gives us a

629
00:19:33,038 --> 00:19:35,708
description of what these methods do and

630
00:19:35,718 --> 00:19:36,998
what arguments they take.

631
00:19:37,238 --> 00:19:39,628
Now, if you know that you had a certain

632
00:19:39,638 --> 00:19:41,348
method available to you, but you couldn't

633
00:19:41,358 --> 00:19:42,918
remember exactly what it does,

634
00:19:43,158 --> 00:19:45,268
then you can actually pass it directly into

635
00:19:45,278 --> 00:19:46,198
help also.

636
00:19:46,438 --> 00:19:47,598
So if I wanted to

637
00:19:48,438 --> 00:19:50,038
find out some more information

638
00:19:50,278 --> 00:19:51,878
about the lower method,

639
00:19:52,198 --> 00:19:55,398
then we could say string dot lower

640
00:19:55,718 --> 00:19:56,918
and if we run that.

641
00:19:57,198 --> 00:19:58,268
Then we can see that it gives us

642
00:19:58,278 --> 00:20:00,628
information and the description just of

643
00:20:00,638 --> 00:20:01,638
what lower does.

644
00:20:01,958 --> 00:20:04,228
So using that DIR function and that help

645
00:20:04,238 --> 00:20:04,678
function

646
00:20:04,918 --> 00:20:06,788
is a great way to get a broad overview of

647
00:20:06,798 --> 00:20:08,388
the methods and attributes that are

648
00:20:08,398 --> 00:20:09,238
available to you.

649
00:20:09,478 --> 00:20:10,438
And also what

650
00:20:10,758 --> 00:20:11,278
using the

651
00:20:11,558 --> 00:20:13,798
help function gives you an idea of what those

652
00:20:14,398 --> 00:20:17,308
methods do without actually going online

653
00:20:17,318 --> 00:20:18,598
and checking everything there.

654
00:20:19,278 --> 00:20:20,708
OK, so I think that is going to do it for

655
00:20:20,718 --> 00:20:21,228
this video.

656
00:20:21,238 --> 00:20:23,158
I hope that now you feel comfortable with

657
00:20:23,238 --> 00:20:25,148
working with strings and are familiar with

658
00:20:25,158 --> 00:20:26,508
some of the useful things that we can do

659
00:20:26,518 --> 00:20:26,998
with those.

660
00:20:27,238 --> 00:20:28,748
In the next video we'll be learning how to

661
00:20:28,758 --> 00:20:30,518
work with numbers in Python

662
00:20:31,078 --> 00:20:33,238
and specifically integers and floats.

663
00:20:33,558 --> 00:20:35,188
But if anyone has any questions about what

664
00:20:35,198 --> 00:20:36,828
we covered in this video, then feel free to

665
00:20:36,838 --> 00:20:38,588
ask in the comments section below and I'll

666
00:20:38,598 --> 00:20:39,638
do my best to answer those.

667
00:20:39,918 --> 00:20:41,508
If you enjoy these tutorials and would like

668
00:20:41,518 --> 00:20:42,868
to support them, then there are several

669
00:20:42,878 --> 00:20:43,638
ways you can do that.

670
00:20:43,958 --> 00:20:45,708
The easiest way is to simply like the video

671
00:20:45,718 --> 00:20:46,598
and give it a thumbs up.

672
00:20:46,838 --> 00:20:48,348
And also it's a huge help to share these

673
00:20:48,358 --> 00:20:50,028
videos with anyone who you think would find

674
00:20:50,038 --> 00:20:50,598
them useful.

675
00:20:50,798 --> 00:20:52,068
And if you have the means, you can

676
00:20:52,078 --> 00:20:53,588
contribute through Patreon and there's a

677
00:20:53,598 --> 00:20:55,068
link to that page in the description

678
00:20:55,078 --> 00:20:55,718
section below.

679
00:20:56,118 --> 00:20:57,948
Be sure to subscribe for future videos and

680
00:20:57,958 --> 00:20:58,708
thank you all for watching.

