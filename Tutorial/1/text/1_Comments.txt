Before we wrap up, here's an additional tip about comments in Python. 
Comments are a great way to add notes or explanations in your code for reference, 
and they are especially helpful for beginners. To make a comment, 
just put a 'hashtag' at the beginning of the line, 
and whatever you type after it will be ignored by <PERSON> as code. 
Now, run the code. See, the comment doesn't affect the code at all—it's just a comment. 
The interpreter will skip over it, not considering it as part of the code.
And it's not just for beginners. 
Imagine you're working on a project with thousands of lines of code. 
If you haven't left comments explaining what certain blocks of code do, 
you might forget why you wrote them when you come back after a month. 
So, in our example, you could add comments like 'hashtag', dot-formatting method over here, 
and 'hashtag', 'f-string' method over here, to clearly explain what each part of the code is doing.
But that's not all. 
Comments can also be used to temporarily disable code. 
If there's a line you don't need to run at the moment, 
just add a hashtag in front of it, 
and it will be treated as a comment, not as code. 
When you need that code back, just remove the hashtag.
You don't even have to manually add hashtags by moving your cursor to the beginning of each line.
there's a shortcut! Just place your cursor anywhere in the line and press "control-key', plus, "question-mark-key". 
This will add the hashtag and turn the line into a comment. 
To uncomment it, do the same.
"control', plus, "question-mark".
This works for multiple lines too.
select the lines, 
press "control', plus, "question-mark", and the entire block will be commented or uncommented.
Commenting is one of the most necessary things in coding, 
helping you and others who might read your code understand its purpose. 
It's a small habit with a big impact on readability and maintainability."