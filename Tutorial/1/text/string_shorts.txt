And if we counted these up,
one-two-three-four-five-six-seven-eight-nine-ten, eleven.
Notice that the space between 'Hello' and 'World',
is also counted as a character, making the total length eleven.
Remember, in Python, everything inside the quotes, counts as part of the string, 
including 'spaces, punctuation, and any special characters'.
And we can access our string's characters
individually.
So to do this, we can use, 
'the square brackets'.
after our string, and pass in the location
of the character that we want.
So I'll say. 
print 'message. 
and then, 'square brackets'.
Now the location is called an 'index'.
and it starts out as 'zero'.
So to access the first character of our
string, we can say.
print 'message',
and then access this.
Index of
Zero.
So if we print that,
then we can see, that we got the 'capital H'.
Now,
since the length of our string, is eleven,
that means, that with the first character
starting out at
Zero,