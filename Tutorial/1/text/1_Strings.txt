Hey there! how's it going everybody?
In this video we'll be learning about
Python data types, and specifically we'll
be learning about, how to work with textual data.
And textual data in Python are represented
by strings.
To display text in Python, we use the print-function. 
Let's start by printing something on the screen.
print, 'Hello World'.
Now, this line here is using the print-function.
and we're passing this text value into that
print function.
if you run this, you'll see that it prints 'Hello-World' in the console. 
Now if we wanted to create a variable that
holds that text value,
then we can just create a variable.
For instance we create, 'MESSAGE'.
and we'll set that 'MESSAGE' variable, equal-to, 
our text value that we passed in to print.
And here, 'MESSAGE' is our variable name.
So if you're coming from another language,
then you might be wondering if we need to
use semicolons, or something like that to
end each line.
But Python doesn't need that.
It operates on whitespace alone, which
makes it a very clean language to work with.
Now, by convention, our variables are
usually all lowercase,
and if it's multiple words,
then we separate those with under score.
So if instead my variable name was 'MY-MESSAG<PERSON>'.
Then it would be 'MY'. under score. 'MESSAGE'.
So that's just a convention that's commonly used.
And also these variable names should be, 
as descriptive as possible, as to what values
they're meant to hold.
So 'MESSAGE' is a good variable name here,
because it's holding our message.
But if I was instead to call this
variable 'M', which is a valid variable name,
but anyone reading my code now, wouldn't
know, when they see this 'M' variable what
value it's supposed to hold.
So 'MESSAGE' is a much
better variable name in this case.
So this 'MESSAGE' variable now holds our text data,
and our text data is called 'a STRING'.
Now we can use our variable in place of
this 'STRING',
anywhere that we use it in our program.
So instead of printing out 'hello-world'
directly here,
I'm instead going to now print out this
variable
and that should give us the same result.
So if I run that
then you can see down here we still get the
same result.
Now we can see that I created this string
with single-quotes.
Now you can also use double-quotes.
So if you're wondering if there's a
difference between the single-quotes and
the double-quotes,
then it really just depends on what text
you have in your string.
So for example, if you have a single-quote
in your string,
so for example, let's create one with a
single quote.
Instead of 'Hello-World',
I will instead make our string,
'Benjamin's world'.
Now see, the problem here is that
Python sees this single-quote within our text,
as being the end of the string, and it's
not going to know, what to do, with what
comes after that single-quote here,
because it thinks that's where the string
is closing.
So if you run this now, then you'll get an error.
Now there's a couple of different ways to
handle this.
We could,
escape this single-quote with a back-slash.
So, if I escape that single-quote and now
run this,
now Python knows, that this single-quote
doesn't closeout the string.
and that instead this one should.
Now another way to handle that is,
So I'll take away that escape character.
Now another way to handle that is to
instead just use double quotes on the
outside of our string,
any string that contains single-quotes.
So, if we know that our string contains a
single-quote, then we can instead use
double-quotes on the outside.
and then it'll know that that single-quote
isn't the end of the string.
So, now if we run this, then we can see that
it still works fine.
But that doesn't necessarily mean that
double-quotes are better because it goes
both ways.
If your string contains double-quotes in
the message,
then I would use, single-quotes on the
outside.
Now if we needed to create a multi line
string,
then one way we can do this, is by using
three quotes at the beginning, and end of
our string.
And these can also, be single or double
quotes as well.
So let's go ahead and look at an example.
So I'll add three quotes to the beginning and
end of our string here.
And now I'll just add some text to span
some multiple lines here.
So I'll just say,
was peaceful, and then, hit enter to go to a
new line.
and say,
until Mingo arrived.
So now if we run that,
then we can see that it printed out our
string correctly, over multiple lines.
OK, so let's go back to,
our simple 'Hello-World' example.
So I'm just going to take, all of that.
and replace it with.
our previous 'Hello-World' example.
And now let's go ahead, and just, run that
really quick.
So we're back to our starting point.
So we can think of our string. as a string.
of individual characters,
and we can access these individual
characters also.
So first, let's see how we can, find how
many characters are in our string.
So to do this, we can use the, 'L', 'E', 'N', function, which stands for 'LENGTH'.
So,
whenever I print out here, if I was instead
of printing 'my-message',
if I was to print out.
this
'length-function'. and pass in 'message'. and
then run this.
Now we're no longer printing out our
'message', we're printing out, the 'length of
our message',
and we can see that, it says that the length
of our string, is 'eleven'.
And if we counted these up,
one-two-three-four-five-six-seven-eight-nine-ten, eleven.
Notice that the space between 'Hello' and 'World',
is also counted as a character, making the total length eleven.
Remember, in Python, everything inside the quotes, counts as part of the string, 
including 'spaces, punctuation, and any special characters'.
And we can access our string's characters
individually.
So to do this, we can use, 
'the square brackets'.
after our string, and pass in the location
of the character that we want.
So I'll say. 
print 'message. 
and then, 'square brackets'.
Now the location is called an 'index'.
and it starts out as 'zero'.
So to access the first character of our
string, we can say.
print 'message',
and then access this.
Index of
Zero.
So if we print that,
then we can see, that we got the 'capital H'.
Now,
since the length of our string, is eleven,
that means, that with the first character
starting out at
Zero,
our last character would be at index TEN.
So, it's our total length, minus-one.
So if I was to say, print-out
the location, at index TEN, the value at
index TEN,
then we can see that, we got our 'D'
character, which is the last character in
that string.
Now, if you accidentally try to access an
index that doesn't exist, then you'll get
an index error.
So if we were to say,
access the
index of eleven,
then we can see that that threw an index error.
Now we can also access a range of
characters.
So if I just wanted to get the word, 'Hello'.
from our string,
then we could say,
that we want,
zero, and then this 'colon Five'.
So the first index here is our starting point.
And the second index, which is separated by
this colon, is the stopping point.
Now, one thing a little confusing about this,
is that the first index is inclusive, which
means it's going to include that value,
but the second index is not.
Now, there's good reasons for this, but
it's still easy to forget.
So,
basically what we're saying is, I want all
the characters,
between the beginning, and up to but not
including, 
the fifth-index. 
and it'll be more clear, if we just go ahead and
run this.
So we can see that, it prints out
'hello'.
So it printed out our 'message', from the zero
index here.
all the way up to, but not including the fifth
index here.
So we got, 'hello'.
Now since our starting point here, is just
the first index, we can actually just leave
that off,
and it'll assume that we want to start at
the beginning.
So if we don't put anything there, and then
colon-five,
then we should, get the same thing.
So if we run that, we can see we still got 'hello'.
Now instead, if we wanted to grab the word
'world', from the string,
then we could start
at the sixth index.
and then we can just go to the end.
And just like leaving off our starting
index, it will start from the beginning.
Leaving off the stop index, just goes all
the way to the end.
So now if we run that, then we can see that
it gives us back, the word 'world'.
Now, what we're doing here is called
'slicing', and if you'd like to dive deeper into it, 
I'll be covering it in more detail in an upcoming video.
So, now let's just go back to printing out,
our 'message', and let me run this.
OK, so all of the data types that we're
going to review, are going to have certain
methods, available to us that give us access
to a lot of useful functionality.
Now, when I say methods,
a lot of people wonder, what's the
difference between, 'a method', and 'a function'.
And 'functions' and 'methods' are basically the
same thing.
A 'method' is just a 'function' that belongs to
an object.
It's not important to get into the details
of that now,
but if you hear me say 'method' or 'function',
then you can basically think of those as
the same thing for now.
So like I was saying, the data types that
we'll be going over,
all have certain methods available to us,
that give us access to a lot of useful
functionality.
So let's look at some of these, string
methods.
So we can see here that our 'hello world' text
is capitalized,
but let's say that we wanted
that to be all lowercase.
Now to do this, it's just as easy as saying
print, 'message'
and then, to 'lowercase' this we can say
dot lower.
Now when we run this 'dot lower' with these
parentheses here,
that's running the 'lower' method on the
string.
So when we ran that, we can see that now
our 'hello world' has been set to all
lowercase.
And if we wanted to do this to uppercase,
then it's as easy as changing that lower, to upper.
If we run that,
now we can see that hello world is all
uppercase.
So now let's say that we want to count a
certain number of characters in our string.
And to do that we can use the count method. And
the count method actually takes a string, as
an argument.
So we can say,
'message', dot count.
and now, we have to pass in an argument.
And it has to know what we want to count in
our message.
So for now we'll just say count 'hello'.
And if we run that,
we can see that it returns that the string,
'hello' appears in our message, one time.
But if we instead, just passed in a single
character, as our argument, so I'll pass in an 'EL'
If I run that,
you can see that we get
a 'three'. because there are three 'EL' in our
'message' variable.
So if we instead want to find, the index, of
where certain characters can be found,
then we can use the 'find-method'.
So I could come up here and instead of
saying 'dot-count', I can say, 'dot-find'.
And now this takes an argument as well.
It's what we want to 'find'.
So, let me type in, 'world' here, and run this.
and we can see, when we run this, that it
returns a 'six'.
and that's because, 'world', starts at the sixth
index, of our 'message' variable.
Now ,if we try to find a string of
characters that doesn't exist.
Then it will just return,
'Negative-one'.
So, if instead of, 'world', I typed in, 'universe'.
and ran that,
you can see it returns, a 'Negative-one', because it
can't find that anywhere in our 'message'
variable.
OK, so now let's say, that we want to
replace some characters, in our 'string', with
some other characters,
and we can do this with the, 'replace' method.
Now first I'm going to change this, back to printing out, our
regular 'message', so I'll just delete these.
Now let's try to use our 'replace' method,
right below, where we first set our 'message'
variable.
And, this method takes two arguments.
First, it takes, what we want to replace.
So first, let me just say 'message'. 'dot-replace'.
So first it takes, what we want to replace.
So let's say that we want to replace,
'world'.
And now, the second argument, which is
separated by a comma,
is what we want to replace, 'world' with.
So we'll replace, 'world' with, 'universe'.
So now if we run this,
then this might be, a little unexpected.
We can see that, it's still printing out
'hello world'.
Now the reason, our replacement didn't work,
is because, it's not making that replacement
in place.
It's actually returning a new string, with
those values replaced.
And we'll learn more about return
statements, when we learn about functions.
But basically,
we need to set, a new variable here,
to get that returned string, with those
replacements.
So, I could say something like, 'new-message'.
is equal to,
that original method, with,
this replacement.
So, now if we set that, new variable.
and instead print that 'new-message', and run that,
then you can see that now, it replaced 'world',
with 'universe'.
And if you really wanted to make the
replacement to the 'message' variable,
then instead of making a 'new-message'
variable,
then we can just set this same 'message'
variable again.
So now we're setting the same 'message'
variable, equal to that replacement, and then
printing it out.
So, if I run that,
then we can see that now the 'message'
variable had 'world', replaced with 'universe'.
And this may look a little strange, to set a
variable to,
an altered version of itself, but it's very
common and you'll be using that a lot.
OK, so now let's get rid of this replace
line here,
and now let's look at how we can 'add'
multiple strings, and concatenate them
together.
So instead of saying 'hello-world',
I'm instead just going to set this equal to 'hello'.
and instead of calling this 'message', I'm
going to set this equal to 'greeting'.
And just below greeting, I'm going to
create a variable, called 'name', and
I'm going to set that equal to,
we'll just say 'Steve'.
And now lastly, let's create a message here.
and, we want this message, to combine our
greeting with our name.
We want it to say, 'hello Steve'.
And one way to do this is to use the plus
sign operator. 
So, we could try this by saying 'greeting', plus, 'name'.
Now, if we run this.
and we can see that it's not exactly what
we wanted.
It combined them together,
but it doesn't have a 'space' there.
So when you're concatenating strings
together,
it's easy to make mistakes like this.
So, what we wanna do is 'add' a string between them
that spaces them out.
So we can 'add' a string between these,
just by, putting in a 'string-literal' here.
And I'm also gonna put in a 'comma' and a
'space' to separate those.
And now, we also need another 'plus' sign, 
so that we're adding the 'name', after that
string.
So now we're saying that we want this
'greeting' which is 'hello',
plus, this 'comma', and 'space',
plus the 'name'.
So if I run this.
then we can see that, it concatenated those
strings together, how we wanted.
Now sometimes using the 'plus' sign isn't the
best way to go.
If we wanted to create a longer more
complicated string,
while using our variables,
then adding them all together like this
might get hard to keep track of.
So let's say that we wanted to add to the
end of our message.
just by.
you know, closing off a sentence and saying
'welcome'.
So to do that, after our 'name' variable, we
could add another string, that is a 'period',
to close off that sentence,
a space,
and then 'welcome' with an exclamation point.
So let's go ahead and run that.
Now we can see that it printed out the
string, how we wanted it to look.
but it's starting to get a little
complicated on this line here, to keep track
of all of our 'plus' signs and 'spaces',
within our message.
Instead, with strings like this,
it's usually better to use a 'formatted-string'.
This allows us to write the sentence as it
will appear.
and put placeholders in place of our
variables.
So let's go ahead, and see what this would
look like.
So to instead use a 'formatted-string', we
can say 'message', is equal to.
And we're just going to write it exactly
how it appears,
except everywhere, where we want to replace
with a variable.
We're going to put in a placeholder, and
those placeholders are going to be these
'curly brackets'.
So we want a placeholder for the 'greeting',
and then a 'comma', 'a space',
then a placeholder for the 'name',
then a period,
and then we'll type out 'welcome',
and an exclamation point.
And now to fill those placeholders, with our
variables, we can say 'dot'. 'format'.
And pass in 'greeting', for the first
placeholder.
and then 'name', for the second placeholder.
So now let's go ahead, and delete this line,
where we were using ,the plus sign
operator.
So if we run this,
we can see that we got the same thing as
when we concatenated them together.
At this point, you might think this is easier than concatenation.
However, 'dot-formatting' can get more confusing, as you start using more variables. 
For example, let's say we have four variables, 
'name' equals to, 'Ben'. 
'place' equals to, 'East Blue'.
'food' equals to, 'Meat'. 
'fear' equals to, 'water'.  
If you want a statement like: 'Hey, my name is Ben. I'm from East Blue. I love Meat and I'm afraid of water.' 
This is how you can do it in, 'dot-format' method.
Hey, my name is, 'curly brackets'. I'm from, 'curly brackets', I love, 'curly brackets', and I'm afraid of, 'curly brackets'.
Then, 'dot-format', and pass the variables. 
name, comma, 
place, comma, 
food comma, fear.
Now Run the code. 
its done. 
But, This can be tricky. 
The longer the statement, the more likely you are to misplace variables, 
like accidentally putting 'food' in place of 'place'. 
Tracking all the positions of the variables in the 'curly braces' can be confusing.
This is exactly where our superhero, the 'f-string,' comes to the rescue. 
With 'f-strings', you can directly reference variables, in-line, 
without worrying about matching their positions.
Just add an 'f', open the quotes, and start writing your statement. 
Hey, my name is.
And, when you need to insert the variable, 
just open 'curly brackets', and place the variable inside.
That's it. 
I'm from, 'place', 
I love, 'food', 
and I'm afraid of, 'fear'.
Now, run it. 
see, how simple it is.
making it much easier to write and read, complex strings. 
I always use 'f-strings', 
because they're so straightforward and efficient.
No matter how many variables, or how long the string is, 
'f-strings' never get confusing.
So if I wanted the 'name' in all caps, 
for some reason, then I could just come into
the placeholder here, and say, 'name-dot-upper'.
and run that directly within the
placeholder.
If I run that,
then you can see that now the name was
capitalized.
Okay, so we're almost finished up, but I
wanted to show you one last trick here when
you're learning something new in Python.
So we saw a lot of different methods that
we could use on our strings.
Now, if we ever wanted to see everything
that's available to us, then there are a
couple of things that we can do.
So first we can use this, 'D-I-R' function, 
and that looks like this.
And what this does is, if we pass in a
variable,
then it will show us all of the attributes
and methods, that we have access to with
that variable.
Now don't worry about all of these double
under score methods yet,
but if we look down here, then we'll see a
couple of familiar methods, that we used in
this video.
So for example, we have dot upper here, we have
dot replace
dot lower.
So this kind of gives you a broad overview
of all the attributes, and methods that are
available to you.
Now this doesn't show what any of these
actually do.
So to see more information, about these
string methods,
then we can use the 'help' function.
But if we run this 'help' function on the 'name',
then that won't work.
We actually need to run it on the string
class itself.
So we'll just type in 'help', and then 'S-T-R' for
string.
And if I run that,
then we can see that this gives us a lot
more information and I'll pull this up here a bit.
So it,
you know, tells us everything that's
available to us.
And if I Scroll down here a bit
and if I try to find something that we
actually used in this video, OK, so we have
'find' here.
So you can see that it actually gives us a
description of what these methods do and
what arguments they take.
Now, if you know that you had a certain
method available to you, but you couldn't
remember exactly what it does,
then you can actually pass it directly into
'help' also.
So if I wanted to
find out some more information
about the 'lower' method,
then we could say, 'string-dot-lower'
and if we run that.
Then we can see that it gives us
information and the description just of
what 'lower' does.
So using that 'D-I-R' function', and that 'help'
function,
is a great way to get a broad overview of
the methods and attributes that are
available to you.
And also what
using the
help function gives you an idea of what those,
methods do without actually, going online
and checking everything there.
OK, so I think that is going to do it for
this video.
I hope that now you feel comfortable with,
working with strings and are familiar with
some of the useful things that we can do
with those.
In the next video, we'll be learning how to
work with 'numbers' in Python.
and specifically 'integers' and 'floats'.
But if anyone has any questions about what
we covered in this video, then feel free to
ask in the comments section below, and I'll
do my best to answer those.
If you enjoy these tutorials, and would like
to support them, then there are several
ways you can do that.
The easiest way is to simply like the video
and give it a thumbs up.
And also it's a huge help to share these
videos with anyone who you think would find
them useful.
Be sure to subscribe for future videos, and
thank you all for watching.
Happy Coding.
