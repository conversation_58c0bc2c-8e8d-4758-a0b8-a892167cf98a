import re

def extract_text_from_subtitle(input_file, output_file):
    with open(input_file, 'r') as file:
        lines = file.readlines()

    # List to store the extracted sentences
    sentences = []

    # Regex to identify subtitle timestamp lines and number-only lines
    timestamp_pattern = re.compile(r'\d{2}:\d{2}:\d{2},\d{3}')
    number_pattern = re.compile(r'^\d+$')  # Matches lines containing only numbers

    for line in lines:
        # Ignore lines that are either empty, contain timestamps, or are just numbers
        if not line.strip() or timestamp_pattern.search(line) or number_pattern.match(line.strip()):
            continue
        # Add the remaining lines (sentences) to the list
        sentences.append(line.strip())

    # Write the extracted sentences to the output file
    with open(output_file, 'w') as file:
        for sentence in sentences:
            file.write(sentence + '\n')


# Example usage
input_file = '1_Strings.srt'  # Replace with the path to your subtitle file
output_file = '1_Strings.txt'     # Replace with the path for the output file
extract_text_from_subtitle(input_file, output_file)
