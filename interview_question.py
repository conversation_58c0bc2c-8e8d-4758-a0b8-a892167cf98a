# One Python Interview Question a Day
# Question Number : 1

# What are some features of the Python language?
"""
Interpreted Language. Python code runs without the need for compilation.

Dynamically Typed. You don’t need to declare variable types;
Python determines them dynamically.

Object-Oriented. Python supports classes, composition, and inheritance
but does not have access specifiers like public or private.

First-Class Functions. Functions can be assigned to variables,
returned by other functions, or accepted as parameters.

General-Purpose Language. Python is used in automation, web applications,
machine learning, big data, and more.

Ease of Coding. Python code is quick to write but
runs slower than compiled languages.
"""
