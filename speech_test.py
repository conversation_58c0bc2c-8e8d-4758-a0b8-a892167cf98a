import speech_recognition as sr
import nltk
nltk.download('punkt')

from nltk.tokenize import sent_tokenize

nltk.download('punkt')  # Download the sentence tokenizer data

AUDIO_FILE = "test_audio.wav"
OUTPUT_TEXT_FILE = "audio.txt"
OUTPUT_SRT_FILE = "audio.srt"

r = sr.Recognizer()

with sr.AudioFile(AUDIO_FILE) as source:
    audio = r.record(source)

    # Transcribe using Google API
    googletext = r.recognize_google(audio)

    # Save transcribed text to a text file
    with open(OUTPUT_TEXT_FILE, "w") as text_file:
        text_file.write(googletext)

    # Perform sentence segmentation
    sentences = sent_tokenize(googletext)

    # Save transcribed text with timestamps to an SRT file
    srt_content = ""
    for i, sentence in enumerate(sentences):
        start_time = i * 2  # Assuming each sentence lasts for 2 seconds
        end_time = (i + 1) * 2
        timestamp = f"{i + 1}\n00:00:{start_time:02d},000 --> 00:00:{end_time:02d},000\n"
        srt_content += f"{timestamp}{sentence}\n\n"

    with open(OUTPUT_SRT_FILE, "w") as srt_file:
        srt_file.write(srt_content)
