import cv2
import pytesseract

# Set the path to the Tesseract executable
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Read the image using OpenCV
image_path = 'invoice_1.png'
img = cv2.imread(image_path)

# Convert the image to grayscale
gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# Use Tesseract OCR to extract text from the image
text = pytesseract.image_to_string(gray_img)

# Print the extracted text
print(text)
