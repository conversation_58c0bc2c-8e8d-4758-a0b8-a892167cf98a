from PIL import Image
import numpy as np
from sklearn.cluster import KMeans
from scipy.spatial import distance
import time

""" brown, grey, orange, purple, yellow, white, black, blue, green, red, pink """

# Predefined list of common colors with their RGB values
COLOR_NAMES = {
    "brown": (150, 75, 0),
    "grey": (128, 128, 128),
    "orange": (255, 165, 0),
    "purple": (128, 0, 128),
    "yellow": (255, 255, 0),
    "white": (255, 255, 255),
    "black": (0, 0, 0),
    "blue": (0, 0, 255),
    "green": (0, 128, 0),
    "red": (255, 0, 0),
    "pink": (255, 192, 203),
    "violet": (238, 130, 238)
}

def load_image_as_array(image_path):
    """
    Loads an image from a file path and converts it to a NumPy array.

    :param image_path: Path to the image file
    :return: NumPy array representing the image
    """
    image = Image.open(image_path)
    return np.array(image)

def detect_top_two_dominant_colors(image_array):
    """
    Detects the top two dominant colors in the upper half of the image and maps them to predefined color names.

    :param image_array: The NumPy array representing the cropped image
    :return: A list of the top two dominant RGB colors
    """
    # Convert the NumPy array to a PIL Image
    image = Image.fromarray(image_array)

    # Get image dimensions
    width, height = image.size

    # Focus on the upper half of the image (shirt area)
    upper_half = image.crop((0, 0, width, height // 2))

    # Convert the upper half to a NumPy array
    upper_half_array = np.array(upper_half)
    upper_half.save("upper_half.png")

    # Reshape the image to be a list of pixels (for k-means clustering)
    pixels = upper_half_array.reshape(-1, 3)

    # Use KMeans clustering to find the two most dominant colors (k=2)
    kmeans = KMeans(n_clusters=1)
    kmeans.fit(pixels)

    # Get the two dominant colors
    dominant_colors = kmeans.cluster_centers_.astype(int)
    print(dominant_colors)

    return [tuple(color) for color in dominant_colors]

def map_color_to_name(rgb_color):
    """
    Maps the RGB color to the closest predefined color name using Euclidean distance.

    :param rgb_color: The detected dominant RGB color
    :return: The name of the closest matching color
    """
    closest_color = None
    min_distance = float('inf')

    # Iterate through the predefined colors and find the closest match
    for color_name, color_rgb in COLOR_NAMES.items():
        dist = distance.euclidean(rgb_color, color_rgb)
        if dist < min_distance:
            min_distance = dist
            closest_color = color_name

    return closest_color

# Example usage with image loaded from path
image_path = r"person.png"  # Replace with your actual image path
stime = time.time()
cropped_person_array = load_image_as_array(image_path)  # Load image as NumPy array
dominant_rgbs = detect_top_two_dominant_colors(cropped_person_array)  # Detect top 2 dominant RGB colors

# Map the dominant RGB colors to their closest color names
color_names = [map_color_to_name(rgb) for rgb in dominant_rgbs]

print(f"Top two dominant shirt colors: {color_names}")
etime = time.time()
print(f"Time taken: {etime - stime} seconds")
