import numpy as np
import cv2
from sklearn.cluster import KMeans

# Define color ranges as (min_rgb, max_rgb)
COLOR_RANGES = {
    "brown": ((100, 30, 0), (150, 75, 50)),  # Adjusted range
    "grey": ((75, 75, 75), (200, 200, 200)), # Neutral grey
    "orange": ((200, 80, 0), (255, 165, 0)), # Bright orange
    "purple": ((128, 0, 128), (255, 0, 255)),  # Purple to magenta
    "yellow": ((200, 200, 0), (255, 255, 100)),  # Bright yellow
    "white": ((200, 200, 200), (255, 255, 255)), # Off white to bright white
    "black": ((0, 0, 0), (50, 50, 50)),  # Near black
    "blue": ((0, 0, 150), (100, 100, 255)), # Dark to bright blue
    "green": ((0, 100, 0), (100, 255, 100)), # Dark to bright green
    "red": ((150, 0, 0), (255, 100, 100)), # Dark to bright red
    "pink": ((200, 100, 100), (255, 182, 193))  # Light pink
}

def load_and_process_image(image_path, out_r=120):
    """
    Loads an image, converts it to RGB, and resizes it.

    :param image_path: Path to the image file
    :param out_r: Desired output height for resizing
    :return: Resized image
    """
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    r, c = image.shape[:2]
    image = cv2.resize(image, (int(out_r * float(c) / r), out_r))
    return image

def get_dominant_colors(image, n_clusters=8):
    """
    Uses KMeans clustering to find the dominant colors in an image.

    :param image: The input image
    :param n_clusters: Number of clusters for KMeans
    :return: Dominant colors and their percentages
    """
    pixels = image.reshape((-1, 3))
    km = KMeans(n_clusters=n_clusters)
    km.fit(pixels)

    # Get colors and their percentages
    colors = np.asarray(km.cluster_centers_, dtype='uint8')
    percentage = np.asarray(np.unique(km.labels_, return_counts=True)[1], dtype='float32')
    percentage = percentage / pixels.shape[0]

    # Create a list of dominant colors with their percentages
    dominance = sorted([[percentage[ix], colors[ix]] for ix in range(km.n_clusters)], key=lambda x: x[0], reverse=True)
    return dominance

def get_top_two_colors(dominance):
    """
    Returns the top two dominant colors based on the percentage of their occurrence.

    :param dominance: List of [percentage, color] for each cluster.
    :return: List of top two dominant colors.
    """
    return [dominance[0][1], dominance[1][1]]  # Return the RGB values of the top two colors

def get_color_name(rgb_color):
    """
    Returns the color name based on the RGB value by checking against predefined ranges.

    :param rgb_color: Tuple of RGB values (r, g, b)
    :return: Color name or 'unknown' if not matched
    """
    for color_name, (min_rgb, max_rgb) in COLOR_RANGES.items():
        if all(min_rgb[i] <= rgb_color[i] <= max_rgb[i] for i in range(3)):
            return color_name
    return 'unknown'

def main(image_path):
    image = load_and_process_image(image_path)  # Load and process image
    dominance = get_dominant_colors(image)  # Get dominant colors
    top_two_colors = get_top_two_colors(dominance)  # Extract top two colors

    # Print the RGB values and color names of the top two dominant colors
    print("Top two dominant colors (RGB):")
    for idx, color in enumerate(top_two_colors, start=1):
        color_name = get_color_name(tuple(color))
        print(f"Color {idx}: RGB {tuple(color)}, Name: {color_name}")

# Example usage
image_path = "person.png"  # Replace with your actual image path
main(image_path)
