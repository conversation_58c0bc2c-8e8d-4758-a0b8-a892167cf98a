import whisper
import re

file_path = r"D:\dhaya\PythonCodeCamp\shorts\1\3\3_audio_source.wav"
output_file = r'D:\dhaya\PythonCodeCamp\shorts\1\3\3_text.txt'

model = whisper.load_model("medium")  # Choose from tiny, base, small, medium, large

result = model.transcribe(file_path, language="ta")  # 'ta' = Tamil
print(result["text"])

pattern = r'\.(?!\d)'
sentences = re.split(pattern, result["text"])

with open(output_file, 'w', encoding='utf-8') as file:
    for sentence in sentences:
        clean_sentence = sentence.strip()
        if clean_sentence:
            file.write(clean_sentence + '.\n')

print(f"Sentences saved in '{output_file}' successfully.")
