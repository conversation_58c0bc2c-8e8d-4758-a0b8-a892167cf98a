import os
import yt_dlp

def ensure_unique_filename(directory, title, ext):
    base_name = f"{title}.{ext}"
    full_path = os.path.join(directory, base_name)
    counter = 1
    while os.path.exists(full_path):
        base_name = f"{title}_{counter}.{ext}"
        full_path = os.path.join(directory, base_name)
        counter += 1
    return full_path

def download_instagram_videos(url_list, output_path='./downloads'):
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    for url in url_list:
        print(f"\nFetching info for: {url}")
        try:
            # Step 1: Get info to extract title
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                title = info.get('title', 'video').replace('/', '_').replace('\\', '_')
                ext = info.get('ext', 'mp4')

            # Step 2: Create a unique filename
            unique_path = ensure_unique_filename(output_path, title, ext)
            filename_template = os.path.splitext(os.path.basename(unique_path))[0]

            # Step 3: Use yt_dlp to download with custom filename
            ydl_opts = {
                'outtmpl': f'{output_path}/{filename_template}.%(ext)s',
                'format': 'bestvideo+bestaudio/best',
                'merge_output_format': 'mp4',
                'quiet': False,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                print(f"Downloading as: {filename_template}.{ext}")
                ydl.download([url])

        except Exception as e:
            print(f"Failed to download {url}: {e}")

# Instagram URLs
video_urls = [
    "https://www.youtube.com/watch?v=fWjsdhR3z3c",
]

download_instagram_videos(video_urls)

