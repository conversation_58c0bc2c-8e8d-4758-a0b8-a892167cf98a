import cv2
import os

# Input video file
video_path = '1/hd.mp4'

# Output folder
output_folder = '1/output_frames'
os.makedirs(output_folder, exist_ok=True)

# Open the video
cap = cv2.VideoCapture(video_path)

# Get frames per second (fps) of the video
fps = cap.get(cv2.CAP_PROP_FPS)

# Frame counter
frame_count = 0
saved_count = 0

while True:
    ret, frame = cap.read()
    if not ret:
        break

    # Save one frame every second
    if frame_count % int(fps) == 0:
        frame_filename = os.path.join(output_folder, f'frame_{saved_count}.jpg')
        cv2.imwrite(frame_filename, frame)
        saved_count += 1

    frame_count += 1

# Release video
cap.release()

print("Frames saved successfully!")
