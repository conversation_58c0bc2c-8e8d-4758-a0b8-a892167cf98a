# www.youtube.com/@PythonCodeCampOrg


import whisper
import re

file_path = r"D:\dhaya\X\python\18.wav"

output_file = r'D:\dhaya\X\python\18_text.txt'

model = whisper.load_model("turbo")
result = model.transcribe(file_path)
print(result["text"])


pattern = r'\.(?!\d)'
sentences = re.split(pattern, result["text"])

with open(output_file, 'w', encoding='utf-8') as file:
    for sentence in sentences:
        clean_sentence = sentence.strip()
        if clean_sentence:
            file.write(clean_sentence + '.\n')

print(f"Sentences saved in '{output_file}' successfully.")




# import re
#
# input_file = r'5\5_text_1.txt'
# output_file = r'5\5_text_1.txt'
#
# # Read the content of the file
# with open(input_file, 'r', encoding='utf-8') as file:
#     content = file.read()
#
# # Regex to find sentences ending with a period (not part of numbers)
# pattern = r'\.(?!\d)' # Negative lookbehind and lookahead for numbers
# sentences = re.split(pattern, content)
#
# # Clean and save each sentence
# with open(output_file, 'w', encoding='utf-8') as file:
#     for sentence in sentences:
#         clean_sentence = sentence.strip()  # Remove leading/trailing whitespace
#         if clean_sentence:  # Skip empty lines
#             file.write(clean_sentence + '.\n')
#
# print(f"Sentences saved in '{output_file}' successfully.")
