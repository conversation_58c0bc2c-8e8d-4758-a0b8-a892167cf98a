import tkinter as tk
from pynput import keyboard
import threading


class VisualKeyboard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.overrideredirect(True)
        self.root.attributes('-topmost', True)
        self.root.configure(bg='black')

        self.main_frame = tk.Frame(
            self.root,
            bg='black',
            padx=10,
            pady=20
        )
        self.main_frame.pack(expand=True, fill='both')

        self.layout = [
            ['`', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', '⌫'],
            ['Tab', 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
            ['Caps', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'", 'Enter'],
            ['ShiftL', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', 'ShiftR'],  # Changed shift keys
            ['CtrlL', '⊞Win', 'AltL', 'Space', 'AltR', '⊞R', '☰', 'CtrlR'],  # Distinguished left/right keys
            ['←', '↓', '↑', '→']
        ]

        self.keys = {}
        self.create_keyboard()
        self.pressed_keys = set()

        self.main_frame.bind('<Button-1>', self.start_drag)
        self.main_frame.bind('<B1-Motion>', self.drag)

    def start_drag(self, event):
        self.x = event.x
        self.y = event.y

    def drag(self, event):
        deltax = event.x - self.x
        deltay = event.y - self.y
        x = self.root.winfo_x() + deltax
        y = self.root.winfo_y() + deltay
        self.root.geometry(f"+{x}+{y}")

    def create_keyboard(self):
        for row_idx, row in enumerate(self.layout):
            row_frame = tk.Frame(self.main_frame, bg='black')
            row_frame.pack(padx=2, pady=1)

            for key in row:
                width = self.get_key_width(key)
                btn = tk.Button(
                    row_frame,
                    text=key,
                    width=width,
                    height=1,
                    relief='raised',
                    bg='#444444',
                    fg='white',
                    activebackground='#666666',
                    activeforeground='white',
                    font=('Arial', 7)
                )
                btn.pack(side=tk.LEFT, padx=1, pady=1)

                store_key = key
                if key == '⌫':
                    store_key = 'backspace'
                elif key == 'Tab':
                    store_key = 'tab'
                elif key == 'Caps':
                    store_key = 'caps_lock'
                elif key == 'Enter':
                    store_key = 'Enter'
                elif key == 'ShiftL':
                    store_key = 'shift_l'
                elif key == 'ShiftR':
                    store_key = 'shift_r'
                elif key == '⊞Win':
                    store_key = 'cmd_l'
                elif key == '⊞R':
                    store_key = 'cmd_r'
                elif key == 'CtrlL':
                    store_key = 'ctrl_l'
                elif key == 'CtrlR':
                    store_key = 'ctrl_r'
                elif key == 'AltL':
                    store_key = 'alt_l'
                elif key == 'AltR':
                    store_key = 'alt_r'
                elif key == '☰':
                    store_key = 'menu'
                elif key == '←':
                    store_key = 'left'
                elif key == '↓':
                    store_key = 'down'
                elif key == '↑':
                    store_key = 'up'
                elif key == '→':
                    store_key = 'right'
                elif key == 'Space':
                    store_key = 'space'

                self.keys[store_key.lower()] = btn

    def get_key_width(self, key):
        special_widths = {
            '⌫': 5,
            'Tab': 4,
            'Caps': 4,
            'Enter': 5,
            'ShiftL': 6,
            'ShiftR': 6,
            'CtrlL': 3,
            'CtrlR': 3,
            '⊞Win': 2,
            '⊞R': 2,
            'AltL': 2,
            'AltR': 2,
            'Space': 12,
            '☰': 2,
            '←': 3,
            '↓': 3,
            '↑': 3,
            '→': 3
        }
        return special_widths.get(key, 2)

    def highlight_key(self, key, pressed=True):
        if key.lower() in self.keys:
            btn = self.keys[key.lower()]
            if pressed:
                btn.configure(bg='#eb1c24', relief='sunken')
            else:
                btn.configure(bg='#444444', relief='raised')

    def on_press(self, key):
        try:
            if isinstance(key, keyboard.Key):
                key_char = key.name
            else:
                key_char = key.char
        except AttributeError:
            key_char = str(key).replace('Key.', '')

        # Handle special keys
        if key_char == 'shift_l':
            self.highlight_key('shift_l')
        elif key_char == 'shift_r':
            self.highlight_key('shift_r')
        elif key_char == 'shift':
            self.highlight_key('shift_l')
        elif key_char == 'ctrl_l':
            self.highlight_key('ctrl_l')
        elif key_char == 'ctrl_r':
            self.highlight_key('ctrl_r')
        elif key_char == 'alt_l':
            self.highlight_key('alt_l')
        elif key_char == 'alt_r':
            self.highlight_key('alt_r')
        elif key_char == 'cmd' or key_char == 'windows':
            self.highlight_key('cmd_l')
        elif key_char == 'left':
            self.highlight_key('left')
        elif key_char == 'down':
            self.highlight_key('down')
        elif key_char == 'up':
            self.highlight_key('up')
        elif key_char == 'right':
            self.highlight_key('right')
        elif key_char == 'space':
            self.highlight_key('space')
        else:
            self.highlight_key(key_char)

        self.pressed_keys.add(key_char)

    def on_release(self, key):
        try:
            if isinstance(key, keyboard.Key):
                key_char = key.name
            else:
                key_char = key.char
        except AttributeError:
            key_char = str(key).replace('Key.', '')

        # Handle special keys
        if key_char == 'shift_l':
            self.highlight_key('shift_l', False)
        elif key_char == 'shift_r':
            self.highlight_key('shift_r', False)
        elif key_char == 'shift':
            self.highlight_key('shift_l', False)
        elif key_char == 'ctrl_l':
            self.highlight_key('ctrl_l', False)
        elif key_char == 'ctrl_r':
            self.highlight_key('ctrl_r', False)
        elif key_char == 'alt_l':
            self.highlight_key('alt_l', False)
        elif key_char == 'alt_r':
            self.highlight_key('alt_r', False)
        elif key_char == 'cmd' or key_char == 'windows':
            self.highlight_key('cmd_l', False)
        elif key_char == 'left':
            self.highlight_key('left', False)
        elif key_char == 'down':
            self.highlight_key('down', False)
        elif key_char == 'up':
            self.highlight_key('up', False)
        elif key_char == 'right':
            self.highlight_key('right', False)
        elif key_char == 'space':
            self.highlight_key('space', False)
        else:
            self.highlight_key(key_char, False)

        self.pressed_keys.discard(key_char)

    def start(self):
        listener = keyboard.Listener(
            on_press=self.on_press,
            on_release=self.on_release
        )
        listener.start()
        self.root.mainloop()


if __name__ == "__main__":
    keyboard_display = VisualKeyboard()
    keyboard_display.start()




card_number = 1234567812345678

number_string = str(card_number)

stars = '*' * (len(number_string) - 4)
print(stars)

last_four = number_string[-4:]
print(last_four)

masked = stars + last_four
print(masked)