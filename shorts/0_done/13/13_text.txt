You should never, ever do this in Python.
Suppose you have a list of elements.
Next, we're going to iterate through this list.
So for item in items, we're going to do the following.
If item is equal to B, we're going to remove B from our list.
Otherwise, we're going to print that item.
Now, watch what happens when we run this.
What we get as an output is AD and E.
But what happened to <PERSON>? Well, by removing an item while we're iterating through it,
we effectively told Python that B does not exist anymore on the second iteration.
And since it already went through the first iteration and the second iteration,
it considers C to be the second iteration, which means it will move on to D.
But what does all of this mean?
Well, the moral of this story is that you should never modify your lists while you are iterating through them,
because it can lead to unexpected behavior.
If you ever want to modify the original list,
create a temporary list and use that instead.



You should never do this in Python.
Let’s say you’ve got a list of items.
Now, you're looping through them with, — for item in items:.
Inside the loop, you check:
“If the item is 'B'— remove it from the list.
Otherwise, just print it.”
Simple, right?
But when you run this, the output is:
A — D — E!

wait — where’s <PERSON>?
Here’s what went wrong:
When you remove an item while iterating — Python gets confused.
When we remove B — <PERSON> automatically shifts up and takes <PERSON>'s place.
But since the loop has already completed the second iteration,
Python thinks it has already processed C — even though it actually skipped over it.
So next, it moves on to D — completely skipping C.
This is a classic beginner trap.
Well, the moral of this story is that — you Never modify a list while looping through it.
Instead, build a new list or use a temporary one.
Your code will thank you — and so will future you.