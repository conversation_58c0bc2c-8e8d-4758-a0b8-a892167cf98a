Take a look at this script and try to predict what the output is going to be from these four choices.
So a is equal to rec with an input of 3.
In our rec function, with x equal to 3, we'll hit this else statement and return rec with an input of x minus 1, or 2 in this case.
So now we've entered into what's called recursion, in which a function calls itself.
So back to the top, x is now 2, so we will still hit this else statement.
We'll call rec again with x minus 1, or 1 this time.
Back to the top, x is now 1.
We hit the else statement again, and call rec with 1 minus 1, or 0.
And this time, since x is 0, we satisfy this if block and we return 0.
So our answer here should be 0.
So we can run this, and there we go.
Leave a comment if you guessed correctly.
