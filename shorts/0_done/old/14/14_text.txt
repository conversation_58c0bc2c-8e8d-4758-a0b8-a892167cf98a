Hey There!.
Python's while loop has a secret trick that 90% of developers overlook.
Here's a simple while loop.
It checks if i is less than 2, prints i, and then adds 1 each time.
This way we avoid an infinite loop.
When we run this, it prints 0 and 1.
No surprises here.
But here's the hidden gem.
You can add an else block to this while loop.
If the loop finishes naturally, the 'else block' will execute.
Now it prints 0 and 1, and then success.
That's the else block kicking in.
But here's the catch.
If we use break inside the loop, the else block doesn't run, because we interrupted the loop early.
The else only triggers if the loop completes naturally.
subscribe to 'Python Code Camp', or I'll eat all your cookies. Happy Coding!
