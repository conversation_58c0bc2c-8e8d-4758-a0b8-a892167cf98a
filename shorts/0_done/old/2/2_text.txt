Okay, check out this clever little trick in <PERSON>.
Let's say I have a list here with duplicates inside.
What I want to do is remove duplicates while retaining the order of the list.
So initially, I would think to turn this list into a 'set'.
Since 'sets' removes duplicates.
And then turn it back into a list.
But, sets don't maintain order, so this won't work. 
Probably the most common and readable approach to this is to create an, empty new list,
Then say for 'item-in-My-List'.
Then check, 'if item', 'not-in-New-List', then 'New-List-dot-append' that 'item'.
And this works. But today, I saw a really clever one liner that does the same thing.
So let's say New-List is equal to 'dict-dot-from-keys', then supply 'My-List' as the input.
This will create a dictionary, with the values in 'My-List', as the 'keys'.
And since 'dictionary keys' must be unique, you'll see that duplicates are removed, and the order is maintained.
Now, we only want the 'keys' here, so let's put 'dot-keys' at the end, and let's turn this whole thing back into a list.
So let's run this.
And there we go.
Duplicates are removed, and you'll see.
As I keep rerunning this, The order says the same.
Be sure to leave a like if you found this helpful.
'Happy-Coding!'