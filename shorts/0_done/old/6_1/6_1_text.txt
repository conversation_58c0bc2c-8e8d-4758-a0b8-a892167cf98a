Okay! We all know that we can create our own function in python. 
But did you know about this cool feature in python function?!. 
Let's say we have a function called animal, that will take name as the parameter of string type, a
nd then we print the name of the animal. 
And now, if we call the function with the cat as the argument, 
then we are getting the cat as the output, 
but now if we want to print the dog, then we have to again call the function, 
which will then give both the dog and cat as the output. 
But instead of this, we can simply return the function itself. 
What's interesting here is that, how the function will revoke each time, 
with different argument. Now we can chain multiple function calls together, 
each time printing the provided animal argument, and returning the function for the next animal name. 
So now we can call the function, and pass as many parameters as we want in a single line, 
and this function will return the name of all the animals. 
Here, the function is first invoked with the argument cat, and prints it, 
and then it is again invoked with the dog, and prints it, and finally with the elephant. 
That's it. Subscribe to 'Python Code Camp', or I'll eat all your cookies. Happy Coding!