Hey there!
Did you know that in Python if you have a really long integer like this, you can actually add underscores in between to make it a little more readable?
And you can actually run operations on this. because Python recognizes these as just regular integers.
We'll do num_1 times num_2.
We'll print our answer.
And there we go.
But our answer is unreadable, but we can actually fix that in the print statement.
We'll add an 'f-string' and we'll do answer colon comma, and it will print our answer with commas in between.
And you can do the same with underscores.
So that's your path on today.
Be sure to leave a like if you found this helpful.
And now next time, <PERSON>.

