I had no idea you could do this in Python.
Let's say I have a list of names here,
and I want to create a new list that contains the length of each of these names.
So that's simple.
We can just use list comprehension here.
So len-of-name, for name inside of our names list.
So we can run this, and we've got the length of each name here.
But did you know you can actually use this notation to build dictionaries?
It's called, well, you guessed it, dictionary comprehension.
So let's do length is equal to, and we do open curly braces since we're creating a dictionary here.
Then we wanted to find our key value pair.
So our key is going to be the name, then colon, and our value is going to be the length of that name.
So len-of-name.
Then we say for name in our list of names.
So let's run this.
And there we go.
We've got a dictionary now.
So that's your path on today.
Be sure to leave a like if you found this helpful.
And now next time, <PERSON>.
