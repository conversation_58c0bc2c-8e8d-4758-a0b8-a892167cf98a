Do you know this awesome Python trick?
You can use the step argument in list slicing to grab every second item.
Just type 'colon, colon, and two'.
You can see the syntax for slicing here.
By default, it starts from the beginning and goes to the end of the list.
Run the code.
And BOOM! You get every other item from the list!
Want to reverse the list? Just change two to 'negative one'!
When you run it, your list will be reversed.
But wait, there's more! Use 'negative two' to get every second item in reverse order!
And guess what? It works with strings too!
Let's say I write something like this, and when I run it, it says subscribe to 'python code camp'.


