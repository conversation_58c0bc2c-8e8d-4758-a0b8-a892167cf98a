Did you know that you can use these type of codes to print in color in your terminal?
In fact, these are ANSI codes and basically any Unix or Linux terminal and some windows term that allow for ANSI codes allow you to print in color.
And simply by just going and saying, 'print' and I can do an 'F-string' here and say 'red', say this is red.
And what it does is is we simply give you the escape sequence, the code that you want to use for the color and then the ending of that sequence.
And when I go ahead and print this to the terminal, this prints out our text in that color.
And the funny thing is, is I say 'print' and then say 'here'.
It continues that print and maintains that color throughout the rest of your prints.
Unless you do the 'reset' code, which will reset everything back to what you want it to do.
And just like that, we can print in color and I can switch this up and I can say, we're going to do this one is 'white'.
This one is 'blue' and we can print out different colors throughout different parts of our text.
So that's your path on today.
And now next time, <PERSON>.
