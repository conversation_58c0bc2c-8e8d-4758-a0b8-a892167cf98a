# www.youtube.com/@PythonCodeCampOrg
# How to print in color in Python

if __name__ == "__main__":
    red = "\033[31m"
    white = "\033[0m"
    blue = "\033[34m"
    green = "\033[32m"
    yellow = "\033[33m"
    magenta = "\033[35m"
    cyan = "\033[36m"
    reset = "\033[0m"


    print(f"{red}This is red text.{reset}")
    print(f"{green}This is green text.{reset}")
    print(f"{yellow}This is yellow text.{reset}")
    print(f"{blue}This is blue text.{reset}")
    print(f"{magenta}This is magenta text.{reset}")
    print(f"{cyan}This is cyan text.{reset}")
    print(f"{white}This is the default terminal color.{reset}")
