Hey everyone!
Okay! Do you find long numbers like these boring or hard to read?
Let’s fix that and make them human_friendly in just one line of Python!.
All you need is the 'numerize' library.
Install it, and then import it.
From Numerize. Import Numerize.
Now, here’s the magic:
Call the numerize function from the library.
Just write numerize dot numerize, and pass your long number as an argument.
That’s it! Run the code.
And boom!. See? We’ve got the readable format. 'one seventy five K'.
Now let’s try even bigger numbers like, '10, 75, 85, 95'.
Run the code..
And there you go. '10 point seven six million'.
Isn’t that awesome? So go ahead, try it out, and give your numbers a fresh, readable vibe.
And don’t forget to subscribe to Python Code Camp, or I’ll eat all your cookies!
See you next time, and happy coding!