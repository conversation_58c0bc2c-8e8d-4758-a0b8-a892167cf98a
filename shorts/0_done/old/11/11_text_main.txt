Hey There!.
Okay. Are you tired of seeing boring long numbers like this?
Are you tired of seeing boring long numbers like this?
Let's fix that and make them human_friendly in just one line of Python.
All you need is, the Numerize library.
Install it and then import it.
From Numerize, import Numerize.
Now here's the magic.
Call the Numerize function from Numerize library.
Just write Numerize dot Numerize, and pass your long number as an argument.
that's it!.
Run the code.
Boom! See? We've got the readable format, 'one seventy five K'.
Now let's try even bigger numbers like '10, 75, 85, 95'.
Run the code.
And there you go.
'10 point seven_six million'.
Isn't that awesome?
So go ahead, try it out, and give your numbers a fresh, readable vibe.
And don't forget to subscribe to 'Python Code Camp', or I'll eat all your cookies.
See you next time, and happy coding!.
Happy coding!
