Okay. Have you ever run a Python script that takes a while to execute? Instead of sitting around waiting for it to finish, you can have it play a sound when it's done.
Here's an example.
First, import wind sound.
This library comes with Python by default so you don't need to install it.
One thing to note, however, is that it only works for Windows.
If you're on Linux or Mac, you're out of luck.
Then to have Python play a sound, you do windsound.
beep and in parentheses you put two things.
First is the frequency in hertz for the beep and the second is the duration of the beep in milliseconds.
When I run the code, it's going to make a sound.
If you're on Windows, definitely give this library a try.
Subscribe to 'Python Code Camp', or ill eat all your cookies.
Happy coding!
