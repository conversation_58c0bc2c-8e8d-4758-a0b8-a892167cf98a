# www.youtube.com/@PythonCodeCampOrg

# import time
# import winsound
#
# print("Processing...")
#
# time.sleep(5)
#
# print("process Done!")
# winsound.Beep(800, 1000)













dhaya_team = ["ben", "gwen", "naruto"]
keerthana_team = ["naruto", "hinata", "luffy"]

name_list = ["ben", "naruto", "hinata", "luffy", "itachi", "jiraiya", "robin"]

# Q1 : print names in the name_list which are not in dhaya_team
# Q2 : print names in the name_list which are in both dhaya and keerthana team
# Q3 : check how many dhaya_team members are in name_list
# Q4 : check how many keerthana_team members are in name_list

numbers = [1, 2, 3, 4, 5, [6, 7, 8], [9], 10, [11, 12]]

# Q5 :  print all the even numbers. including inside list elements
# Q6 : add 20, 32, 22, 34 in the second inside list.
# Q7 : check if the element is a list and if yes, print its length
# Q8 : print only the elements which are not list.





















