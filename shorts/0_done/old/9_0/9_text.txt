Imagine you're running a Python script that takes a while to complete.
Instead of staring at the screen waiting, wouldn’t it be great if your computer played a sound when it's done?
Let me show you how! Python has a built-in library called winsound that lets you play simple beeps.
Since it comes with Python by default, you don’t need to install anything.
But keep in mind—it only works on Windows. Here’s how it works. Use winsound dot Beep, and pass two arguments.
The first number is the frequency, which controls the pitch of the sound.
The second number is the duration, which decides how long the beep lasts.
When you run the code, you’ll hear a beep sound!
If you're using Windows, give it a try.
Want more fun Python tricks??
Like and subscribe to 'Python Code Camp', or I'll eat all your cookies.
Happy Coding!