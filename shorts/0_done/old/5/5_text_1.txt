Hey There!
Here’s a Python tip that’ll save your code from crashing!
Imagine you're in a project where you track the bank balance.
Here's what we're doing.
we ask the user how much they'd like to deposit, store that value as num, add it to the balance, and then display the updated balance.
But wait! There's a problem.
When we try to add num to the balance, we get a Type-Error.
Why? Because the input is stored as a string, and you can't directly add a string to a float.
To fix this, we'll convert the input to a float.
Great! This works—until the user accidentally types a letter.
At that point, we get a Value-Error, because a letter can't be converted to a float.
To handle this, we'll use a try-except statement.
We'll attempt to convert the input to a float, and if a Value-Error occurs, we'll catch it and notify the user with a warning message.
Next, we'll place all of this in a while True loop.
If the input is valid and successfully converts to a float, we break out of the loop.
If it isn't, the exception is triggered, and we prompt the user again in the next iteration.
Let's test it out! If we type a letter, we see the warning message.
If we type a valid number, it's successfully added to the balance.
Happy Coding!
