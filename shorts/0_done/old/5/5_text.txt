Okay, let's say we're in a game where we have our balance of money in the bank.
So what we're doing is asking for an input of how much to deposit, storing it as num, and then adding num to our balance and printing it out.
However, you'll see that we get a type error when trying to add to our balance.
That's because our balance gets stored as a string using this input method, and you can't add a string to a float.
Okay, so let's turn our input into a float.
You'll see that this works up until the point where the user accidentally types a letter.
We get a value error because it can't be converted to a float.
Okay, so instead, let's use a try accept statement.
Okay, so let's try to turn our input into a float, except if we get a value error from doing that, we want to warn our user.
Then let's wrap all of this into a while true loop.
Now, if we receive a number and it successfully is turned into a float, then we can break out of this while loop.
However, if a value error occurs and we hit this accept statement, we go through another loop iteration and ask the user again.
So let's run this, and typing a letter, we get an error message, and typing a number, and you'll see that it gets added to our balance.