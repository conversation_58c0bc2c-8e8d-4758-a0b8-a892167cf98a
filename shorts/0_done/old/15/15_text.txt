Hey there!.
Many people think we must use parentheses to create a tuple, but it's not the parentheses that define the tuple. it's the comma.
So, if I remove the parentheses now and check its type, you will see it's a tuple.
The only time you don't need a comma is when you create an empty tuple.
Otherwise, if you create a tuple with a single value and check its type, you'll see it's not even a tuple.
It's just the type of the value you added.
To fix this, all you need to do is add a comma after your value, and it will become a tuple again.
Subscribe to Python Code Camp, or ill eat all your cookies.
Happy coding!
