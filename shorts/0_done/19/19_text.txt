Every beginner gets confused when they see this line in Python.
This weird line? It’s actually super important.
It makes sure your code only runs when you execute the file directly, — and NOT when it’s imported into another file.
Let’s see an example. I’ve got a function called greet in 'file-one'.
and i call the funtion.
When I run this file, it prints the text inside the function.— Simple, right?
Now, let’s import this function into another file — 'file-tow'.
from file-one — import Greet.
then call the function.
And let’s run it.
Boom! It printed twice! Why?
Here’s the thing — when you import file-one — Python runs all the code in it, including the greet-call at the bottom.
Then it runs the greet-call in file-two as well.
And this is where the weired line comes to the rescue.
Wrap the bottom part of file-one in this.
Now, the code inside that block only runs if we execute file1 directly — not when it’s imported. 
Run it again — Problem solved.
Hope you learned something new today! For more Python tips, don’t forget to like, share, and subscribe.
Happy Coding!