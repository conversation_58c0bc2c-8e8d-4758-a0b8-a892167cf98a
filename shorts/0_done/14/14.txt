When a beginner sees this module error:
No module named 'pandas'.
They just copy the name pandas from the error:
and run the command —  pip install pandas, in the terminal.
And guess what? — 
That works perfectly. Isn’t it? — 
Okay, now do the same for this error.
No module named 'CV2' — 
Just copy CV2, and run.
pip install CV2 — 
And boom! —  Error again.
Here’s what many beginners don’t know.
The name shown in the error is the module name.
but pip needs the package name.
So don’t ever just copy the name and run pip install.
All you need to do is.
Copy the name from the error.
Go to your browser.
Type pip — and paste the name and hit enter.
In the first link — that’s 'PyPy dot O R G' —
you’ll find the correct command.
So for CV2, the correct package name is.
pip install open-CV-python
Now run this command.
Tadaaaa! —   It works! — 
keep this in mind. —  Happy Coding!
