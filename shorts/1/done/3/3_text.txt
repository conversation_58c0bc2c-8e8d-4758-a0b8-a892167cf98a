Variables.
x equals 1.
x equals x plus 2.
What is it going to print out?
x equals 3.
Then.
Strings,
x.
Then.
y.
Take a guess what x plus y is going to be.
There it is.
Take a guess what zee is going to be now.
We're taking a slice.
It looks like that.
Functions.
we define this function here.
Take f of 5, guess what it's going to be?
It's 7.
Because we added 2 to 5.
'IF statements'.
x is going to be equal to 2.
If x plus 1 equals 3.
print YES.
Is this going to be true? Yes or no?
What do you think? Yes, it is true, so it will print "YES".
If the condition was false, we would use else to print "NO".
If you put this equals 4, it print No, because it's false.
Loops.
For i in range 1 to 100, this is a fancy way of saying, if i is even.
So what is this going to print out?
Take a guess.
It prints out the even numbers.