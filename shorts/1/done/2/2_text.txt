So most developers debug like this.
They scatter print statements everywhere to check values.
It works, but it's messy and repetitive.
Use Ice Cream.
It automatically prints variable names and values.
No extra typing needed.
But wait, let's make it even better.
With one setting, Ice Cream can show your file names and line numbers too.
Now you'll always know exactly where your logs came from.
No more guessing, no more lost print statements.
Would you replace 'print' with this, in your apps?.
Drop a comment below.
Subscribe to 'Python Code Camp', or I'll eat all your cookies.
Happy Coding.