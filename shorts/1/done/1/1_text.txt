What do you think the output of this script will be? We're printing text, but let's make it way cooler.
Instead of a boring print statement, let's use PyFiglet.
Just one line of code, and your text turns into ASCII art.
But here's the best part.
We can switch fonts with just one change.
Let's try it with <PERSON><PERSON>.
Now we get a totally different style.
There are more than 100 to choose from, and your terminal will never be boring again.
So what's the first word you'll turn into ASCII art? Leave a comment below.
