# from pytube import YouTube
#
#
# def download_youtube_video(video_url, save_path=""):
#     try:
#         # Create YouTube object
#         yt = YouTube(video_url)
#
#         # Get the highest resolution stream
#         stream = yt.streams.get_highest_resolution()
#
#         print(f"Downloading: {yt.title}")
#
#         # Download video to the specified path
#         stream.download(save_path)
#         print(f"Download completed! Saved to {save_path if save_path else 'current directory'}")
#     except Exception as e:
#         print(f"An error occurred: {e}")
#
#
# # Example usage
# if __name__ == "__main__":
#     video_url = input("Enter YouTube video URL: ")
#     save_path = r"shorts\youtube_video"
#     download_youtube_video(video_url, save_path)


from pydub import AudioSegment
import subprocess

def extract_audio_with_pydub(mp4_path, wav_path):
    try:
        # Convert MP4 to WAV
        AudioSegment.from_file(mp4_path).export(wav_path, format="wav")
        print(f"Audio extracted and saved to: {wav_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

# Example usage
if __name__ == "__main__":
    mp4_path = r"1_to_do\1\videoplayback (1).mp4"
    wav_path = r"1_to_do\1\1_audio_source.wav"
    extract_audio_with_pydub(mp4_path, wav_path)




















"""

import yt_dlp
import os
import sys


def download_video(url, output_path=None, resolution="best"):
   
    try:
        # Set output path
        if output_path is None:
            output_path = os.getcwd()

        # Ensure the output directory exists
        os.makedirs(output_path, exist_ok=True)

        # Map resolution options
        if resolution == "highest" or resolution == "best":
            format_option = "best"
        elif resolution == "lowest" or resolution == "worst":
            format_option = "worst"
        else:
            # For specific resolutions like 720p, 480p, etc.
            # This will select the best video format with specified height and best audio
            format_option = f"bestvideo[height<={resolution[:-1]}]+bestaudio/best[height<={resolution[:-1]}]"

        # Configure options for yt-dlp
        ydl_opts = {
            'format': format_option,
            'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
            'noplaylist': True,
            'quiet': False,
            'no_warnings': False,
            'progress': True,
            'ignoreerrors': True,
        }

        # Create a progress handler
        class MyLogger:
            def debug(self, msg):
                pass

            def info(self, msg):
                print(msg)

            def warning(self, msg):
                print(f"Warning: {msg}")

            def error(self, msg):
                print(f"Error: {msg}")

        ydl_opts['logger'] = MyLogger()

        # Download the video
        print(f"Fetching video information from: {url}")
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Get video info first
            info = ydl.extract_info(url, download=False)
            if info:
                print(f"Title: {info.get('title', 'Unknown title')}")
                print(f"Duration: {info.get('duration', 'Unknown')} seconds")

                # Then download
                print(f"Downloading video...")
                result = ydl.download([url])

                if result == 0:
                    filename = ydl.prepare_filename(info)
                    print(f"Download complete! Saved to: {filename}")
                    return filename
                else:
                    print("Download failed with an error.")
                    return None
            else:
                print("Could not retrieve video information.")
                return None

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None


if __name__ == "__main__":
    # Example usage
    video_url = input("Enter the video URL: ")
    save_path = input("Enter the save directory (leave blank for current directory): ")

    if save_path == "":
        save_path = None

    # Resolution options
    print("\nResolution options:")
    print("1. Best quality (default)")
    print("2. Lowest quality (smaller file size)")
    print("3. Custom (e.g., 720p, 480p, 360p, etc.)")

    res_choice = input("Choose a resolution option (1-3): ")

    if res_choice == "1" or res_choice == "":
        resolution = "best"
    elif res_choice == "2":
        resolution = "worst"
    elif res_choice == "3":
        resolution = input("Enter the desired resolution (e.g., 720p): ")
    else:
        resolution = "best"

    # Download the video
    download_video(video_url, save_path, resolution)

"""