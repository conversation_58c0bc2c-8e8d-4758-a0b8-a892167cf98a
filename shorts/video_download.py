import yt_dlp
import os
import sys
from pprint import pprint


def list_available_formats(url):
    """
    List all available formats for a given video URL

    Parameters:
    url (str): The video URL

    Returns:
    list: Available formats
    """
    ydl_opts = {
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            if not info:
                print("Could not retrieve video information.")
                return []

            formats = info.get('formats', [])
            if not formats:
                print("No formats available for this video.")
                return []

            # Filter and organize formats
            video_formats = []
            for f in formats:
                # Skip formats without video
                if f.get('vcodec') == 'none':
                    continue

                # Create a readable format description
                format_id = f.get('format_id', 'N/A')
                ext = f.get('ext', 'N/A')
                resolution = f.get('resolution', 'N/A')
                fps = f.get('fps', 'N/A')
                filesize = f.get('filesize')

                # Convert filesize to MB if available
                size_str = 'Unknown size'
                if filesize:
                    size_str = f"{filesize / 1024 / 1024:.2f} MB"

                # Check if this format has audio
                has_audio = f.get('acodec') != 'none'
                audio_str = "with audio" if has_audio else "no audio"

                # Create format description
                format_desc = f"{format_id}: {resolution}, {fps} fps, {ext}, {audio_str}, {size_str}"

                video_formats.append({
                    'format_id': format_id,
                    'description': format_desc,
                    'has_audio': has_audio,
                    'resolution': resolution,
                    'fps': fps,
                    'ext': ext,
                    'filesize': filesize
                })

            return video_formats

    except Exception as e:
        print(f"Error retrieving formats: {str(e)}")
        return []


def download_video(url, output_path=None, format_id=None):
    """
    Download a video using yt-dlp with specific format

    Parameters:
    url (str): The video URL
    output_path (str): Directory to save the video
    format_id (str): Format ID to download

    Returns:
    str: Path to the downloaded video file
    """
    try:
        # Set output path
        if output_path is None:
            output_path = os.getcwd()

        # Ensure the output directory exists
        os.makedirs(output_path, exist_ok=True)

        # Configure options for yt-dlp
        ydl_opts = {
            'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
            'noplaylist': True,
            'quiet': False,
            'no_warnings': False,
            'progress': True,
            'ignoreerrors': True,
        }

        # If specific format is provided, use it
        if format_id:
            ydl_opts['format'] = format_id

        # Create a progress handler
        class MyLogger:
            def debug(self, msg):
                pass

            def info(self, msg):
                print(msg)

            def warning(self, msg):
                print(f"Warning: {msg}")

            def error(self, msg):
                print(f"Error: {msg}")

        ydl_opts['logger'] = MyLogger()

        # Download the video
        print(f"Fetching video information from: {url}")
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Get video info first
            info = ydl.extract_info(url, download=False)
            if info:
                print(f"Title: {info.get('title', 'Unknown title')}")
                print(f"Duration: {info.get('duration', 'Unknown')} seconds")

                # Then download
                print(f"Downloading video...")
                result = ydl.download([url])

                if result == 0:
                    filename = ydl.prepare_filename(info)
                    print(f"Download complete! Saved to: {filename}")
                    return filename
                else:
                    print("Download failed with an error.")
                    return None
            else:
                print("Could not retrieve video information.")
                return None

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None


if __name__ == "__main__":
    # Get video URL
    video_url = input("Enter the video URL: ")

    # Get available formats
    print("Fetching available formats...")
    formats = list_available_formats(video_url)

    if not formats:
        print("No formats available. Exiting.")
        sys.exit(1)

    # Show available formats
    print("\nAvailable formats:")
    print("------------------")

    # Sort formats by resolution and whether they have audio
    formats_with_audio = [f for f in formats if f['has_audio']]
    formats_video_only = [f for f in formats if not f['has_audio']]

    print("\nFormats with video + audio:")
    for i, fmt in enumerate(formats_with_audio):
        print(f"{i + 1}. {fmt['description']}")

    print("\nVideo-only formats (no audio):")
    for i, fmt in enumerate(formats_video_only):
        print(f"{len(formats_with_audio) + i + 1}. {fmt['description']}")

    # Ask user to select format
    while True:
        try:
            format_choice = int(input("\nSelect a format number (or 0 for best quality with audio): "))

            if format_choice == 0:
                selected_format = "bestvideo+bestaudio/best"
                break
            elif 1 <= format_choice <= len(formats_with_audio) + len(formats_video_only):
                if format_choice <= len(formats_with_audio):
                    selected_format = formats_with_audio[format_choice - 1]['format_id']
                else:
                    index = format_choice - len(formats_with_audio) - 1
                    selected_format = formats_video_only[index]['format_id']
                break
            else:
                print("Invalid selection. Please try again.")
        except ValueError:
            print("Please enter a number.")

    # Get save path
    save_path = input("Enter the save directory (leave blank for current directory): ")
    if save_path == "":
        save_path = None

    # Download the selected format
    print(f"\nDownloading format: {selected_format}")
    download_video(video_url, save_path, selected_format)