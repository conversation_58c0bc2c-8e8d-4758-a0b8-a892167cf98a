from flask import Flask, request, render_template

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/send/contact/message', methods=['POST'])
def send_contact_message():
    # Get form data
    name = request.form.get('name')
    email = request.form.get('email_from')
    phone = request.form.get('phone')
    partner_msg = request.form.get('partner_msg')

    # Here you would insert the provided code
    # for handling the contact message submission.

    return render_template('result.html')

if __name__ == '__main__':
    app.run(debug=True)
