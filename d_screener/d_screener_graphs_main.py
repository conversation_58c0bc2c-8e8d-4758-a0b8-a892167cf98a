import time
import os
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Screener custom screen link
SCREEN_URL = "https://www.screener.in/screens/71064/all-stocks/"

# Output folder
SAVE_DIR = "stock_charts"
os.makedirs(SAVE_DIR, exist_ok=True)


def create_driver():
    chrome_options = Options()

    # Try to find Chrome in common locations
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
    ]

    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            chrome_options.binary_location = path
            chrome_found = True
            print(f"Found Chrome at: {path}")
            break

    if not chrome_found:
        print("Chrome not found in common locations. Using system default.")
        # Don't set binary_location, let ChromeDriver find it

    # Add Chrome options for better stability
    chrome_options.add_argument("--headless=new")  # Use new headless mode
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")  # Speed up loading
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("Chrome driver created successfully")
        return driver
    except Exception as e:
        print(f"Error creating Chrome driver: {e}")
        raise


def get_stock_links(driver):
    """Collect all stock links from the screener screen"""
    driver.get(SCREEN_URL)
    WebDriverWait(driver, 15).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, "a[href*='/company/']"))
    )
    links = []
    elements = driver.find_elements(By.CSS_SELECTOR, "a[href*='/company/']")
    for e in elements:
        href = e.get_attribute("href")
        if "/company/" in href and href not in links:
            links.append(href)
    return links


def process_stock(driver, idx, link):
    """Open a stock page, check price, if < 20 take chart screenshot"""
    try:
        print(f"  Loading page...")
        driver.get(link)
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "h1"))
        )

        # Company name - get the second h1 element (index 1)
        company_name = None
        try:
            h1_elements = driver.find_elements(By.TAG_NAME, "h1")
            if len(h1_elements) > 1:
                company_name = h1_elements[1].text.strip()
            elif len(h1_elements) > 0:
                company_name = h1_elements[0].text.strip()
        except:
            pass

        if not company_name:
            company_name = f"Stock_{idx}"

        # Create safe filename
        safe_name = "".join(c for c in company_name if c.isalnum() or c in ['-', '_', ' '])
        safe_name = safe_name.replace(' ', '_')[:50]
        if not safe_name:
            safe_name = f"Stock_{idx}"

        # Current price - get the second span.number element (current price, not market cap)
        current_price = None
        try:
            price_elements = driver.find_elements(By.CSS_SELECTOR, "span.number")
            if len(price_elements) > 1:
                # Index 0 = Market Cap, Index 1 = Current Price
                price_text = price_elements[1].text.replace(",", "").replace("₹", "").strip()
                current_price = float(price_text)
        except:
            pass

        if current_price is None:
            print(f"⚠️ Could not find price for {company_name}, skipping")
            return

        print(f"  {company_name} → Current Price: ₹{current_price}")

        if current_price >= 20:
            print(f"  ⏭️ Skipping {company_name} (price >= ₹20)")
            return

        # Go to chart tab
        print(f"  Opening chart...")
        chart_tab_selectors = [
            "//a[contains(text(),'Chart')]",
            "//a[contains(@href,'chart')]",
            "//button[contains(text(),'Chart')]"
        ]

        chart_tab = None
        for selector in chart_tab_selectors:
            try:
                chart_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                break
            except:
                continue

        if chart_tab is None:
            print(f"  ⚠️ Chart tab not found for {company_name}")
            return

        driver.execute_script("arguments[0].click();", chart_tab)

        # Click "Max" button
        time.sleep(2)
        max_btn_selectors = [
            "//button[contains(text(),'Max')]",
            "//button[contains(text(),'MAX')]",
            "//a[contains(text(),'Max')]"
        ]

        max_btn = None
        for selector in max_btn_selectors:
            try:
                max_btn = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                break
            except:
                continue

        if max_btn:
            driver.execute_script("arguments[0].click();", max_btn)
            print(f"  Set chart to Max timeframe")
        else:
            print(f"  ⚠️ Max button not found, using default timeframe")

        # Wait for chart to load
        time.sleep(3)
        chart_selectors = [
            "div#chart-container",
            ".chart-container",
            "[id*='chart']",
            ".highcharts-container"
        ]

        chart = None
        for selector in chart_selectors:
            try:
                chart = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                break
            except:
                continue

        if chart is None:
            print(f"  ⚠️ Chart not found for {company_name}")
            return

        # Save screenshot
        file_path = os.path.join(SAVE_DIR, f"{idx}_{safe_name}.png")
        chart.screenshot(file_path)
        print(f"  ✅ Saved: {file_path}")

        # Random sleep (anti-bot)
        time.sleep(random.uniform(2, 5))

    except (TimeoutException, NoSuchElementException) as e:
        print(f"  ⚠️ Element not found for {link}: {str(e)[:100]}...")
    except WebDriverException as e:
        print(f"  ❌ WebDriver error for {link}: {str(e)[:100]}...")
    except Exception as e:
        print(f"  ❌ Unexpected error for {link}: {str(e)[:100]}...")


def main():
    print("Starting stock screener...")
    print(f"Output directory: {SAVE_DIR}")

    try:
        driver = create_driver()
    except Exception as e:
        print(f"Failed to create driver: {e}")
        print("Please ensure Chrome browser is installed and accessible.")
        return

    try:
        print("Getting stock links...")
        stock_links = get_stock_links(driver)
        print(f"Found {len(stock_links)} stocks.")

        if not stock_links:
            print("No stock links found. Please check the screener URL.")
            return

        for idx, link in enumerate(stock_links, start=1):
            print(f"\n{'='*60}")
            print(f"Processing {idx}/{len(stock_links)}: {link}")
            print(f"{'='*60}")
            process_stock(driver, idx, link)

            # Restart driver every 5 stocks to prevent crash
            if idx % 5 == 0 and idx != len(stock_links):
                print("Restarting driver to prevent memory issues...")
                try:
                    driver.quit()
                except:
                    pass
                try:
                    driver = create_driver()
                except Exception as e:
                    print(f"Failed to restart driver: {e}")
                    break

        print(f"\nCompleted processing {len(stock_links)} stocks.")
        print(f"Screenshots saved in: {SAVE_DIR}")

    except KeyboardInterrupt:
        print("\nProcess interrupted by user.")
    except Exception as e:
        print(f"Unexpected error in main: {e}")
    finally:
        try:
            driver.quit()
            print("Driver closed successfully.")
        except:
            pass


if __name__ == "__main__":
    main()
