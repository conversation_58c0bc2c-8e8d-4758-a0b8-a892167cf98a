import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# No credentials needed - we'll use Google OAuth

# Your filtered URL
FILTERED_URL = "https://www.screener.in/screen/raw/?sort=current+price&order=asc&source_id=&query=Current+price+%3C+20+AND%0D%0AMarket+Capitalization+%3E+500"

def create_driver():
    chrome_options = Options()
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    # Remove headless mode for testing so you can see what's happening
    # chrome_options.add_argument("--headless=new")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def login_to_screener(driver):
    """Login to screener.in using Google OAuth"""
    try:
        print("Going to login page...")
        driver.get("https://www.screener.in/login/")

        # Wait for and click Google login button
        try:
            google_login_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, 'google') or contains(text(), 'Google')]"))
            )
            google_login_button.click()
            print("Clicked Google login button")
        except:
            # Try alternative selectors for Google login
            possible_selectors = [
                "a[href*='google']",
                "button[data-provider='google']",
                ".google-login",
                "[class*='google']"
            ]

            google_button_found = False
            for selector in possible_selectors:
                try:
                    google_button = driver.find_element(By.CSS_SELECTOR, selector)
                    google_button.click()
                    google_button_found = True
                    print(f"Clicked Google login using selector: {selector}")
                    break
                except:
                    continue

            if not google_button_found:
                print("❌ Could not find Google login button")
                return False

        # Wait for Google login page
        print("Waiting for Google login page...")
        WebDriverWait(driver, 15).until(
            lambda d: "accounts.google.com" in d.current_url or "login" not in d.current_url.lower()
        )

        if "accounts.google.com" in driver.current_url:
            print("🔄 Please complete Google login manually in the browser window...")
            print("The script will wait for you to complete the login process.")

            # Wait for user to complete Google login (up to 2 minutes)
            login_completed = False
            for i in range(120):  # Wait up to 2 minutes
                time.sleep(1)
                current_url = driver.current_url

                # Check if we're back on screener.in and not on login page
                if "screener.in" in current_url and "login" not in current_url.lower():
                    login_completed = True
                    break

                # Show progress every 10 seconds
                if i % 10 == 0 and i > 0:
                    print(f"Still waiting for login completion... ({i}s elapsed)")

            if login_completed:
                print("✅ Google login completed successfully!")
                return True
            else:
                print("❌ Login timeout. Please try again.")
                return False
        else:
            # Already logged in or redirected
            print("✅ Login appears to be successful!")
            return True

    except Exception as e:
        print(f"❌ Login error: {e}")
        return False

def test_filtered_url(driver):
    """Test accessing the filtered URL"""
    try:
        print(f"\nTesting filtered URL: {FILTERED_URL}")
        driver.get(FILTERED_URL)
        
        time.sleep(5)
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Look for stock links
        stock_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='/company/']")
        print(f"Found {len(stock_links)} stock links")
        
        if stock_links:
            print("✅ Successfully found stock links!")
            for i, link in enumerate(stock_links[:5]):  # Show first 5
                href = link.get_attribute("href")
                text = link.text.strip()
                print(f"  {i+1}. {text} - {href}")
        else:
            print("❌ No stock links found")
            
        return len(stock_links) > 0
        
    except Exception as e:
        print(f"❌ Error testing filtered URL: {e}")
        return False

def main():
    
    print("Creating driver...")
    driver = create_driver()
    
    try:
        # Test login
        if login_to_screener(driver):
            # Test filtered URL
            if test_filtered_url(driver):
                print("\n✅ Everything looks good! You can now use the main script.")
            else:
                print("\n❌ Could not access filtered results. Check your URL or filters.")
        else:
            print("\n❌ Login failed. Please check your credentials.")
            
        input("\nPress Enter to close the browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
