import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Set your screener.in credentials here
SCREENER_EMAIL = "<EMAIL>"  # Replace with your email
SCREENER_PASSWORD = "your_password"        # Replace with your password

# Your filtered URL
FILTERED_URL = "https://www.screener.in/screen/raw/?sort=current+price&order=asc&source_id=&query=Current+price+%3C+20+AND%0D%0AMarket+Capitalization+%3E+500"

def create_driver():
    chrome_options = Options()
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    # Remove headless mode for testing so you can see what's happening
    # chrome_options.add_argument("--headless=new")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def login_to_screener(driver):
    """Login to screener.in"""
    try:
        print("Going to login page...")
        driver.get("https://www.screener.in/login/")
        
        # Wait for login form
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.NAME, "username"))
        )
        
        print("Filling login form...")
        # Fill login form
        email_field = driver.find_element(By.NAME, "username")
        password_field = driver.find_element(By.NAME, "password")
        
        email_field.send_keys(SCREENER_EMAIL)
        password_field.send_keys(SCREENER_PASSWORD)
        
        # Submit form
        login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        login_button.click()
        
        # Wait for login to complete
        print("Waiting for login to complete...")
        time.sleep(5)
        
        # Check if login was successful
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"After login - URL: {current_url}")
        print(f"After login - Title: {page_title}")
        
        if "login" not in current_url.lower() and "register" not in current_url.lower():
            print("✅ Login appears successful!")
            return True
        else:
            print("❌ Login may have failed")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False

def test_filtered_url(driver):
    """Test accessing the filtered URL"""
    try:
        print(f"\nTesting filtered URL: {FILTERED_URL}")
        driver.get(FILTERED_URL)
        
        time.sleep(5)
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Look for stock links
        stock_links = driver.find_elements(By.CSS_SELECTOR, "a[href*='/company/']")
        print(f"Found {len(stock_links)} stock links")
        
        if stock_links:
            print("✅ Successfully found stock links!")
            for i, link in enumerate(stock_links[:5]):  # Show first 5
                href = link.get_attribute("href")
                text = link.text.strip()
                print(f"  {i+1}. {text} - {href}")
        else:
            print("❌ No stock links found")
            
        return len(stock_links) > 0
        
    except Exception as e:
        print(f"❌ Error testing filtered URL: {e}")
        return False

def main():
    if SCREENER_EMAIL == "<EMAIL>" or SCREENER_PASSWORD == "your_password":
        print("❌ Please set your actual screener.in credentials in the script!")
        print("Edit SCREENER_EMAIL and SCREENER_PASSWORD variables at the top of this file.")
        return
    
    print("Creating driver...")
    driver = create_driver()
    
    try:
        # Test login
        if login_to_screener(driver):
            # Test filtered URL
            if test_filtered_url(driver):
                print("\n✅ Everything looks good! You can now use the main script.")
            else:
                print("\n❌ Could not access filtered results. Check your URL or filters.")
        else:
            print("\n❌ Login failed. Please check your credentials.")
            
        input("\nPress Enter to close the browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
