import time
import os
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Screener custom screen link (base URL for pagination)
# Note: The raw URL format requires authentication
SCREEN_BASE_URL = "https://www.screener.in/screen/raw/?sort=current+price&order=asc&source_id=&query=Current+price+%3C+20+AND%0D%0AMarket+Capitalization+%3E+500"

# Login settings
USE_LOGIN = True  # Set to True to enable Google login for accessing filtered URLs
MAX_PAGES = 2  # Maximum number of pages to process (set to 2 for testing)
START_PAGE = 1  # Page to start from (change this to resume from a specific page)

# Output folder
SAVE_DIR = "stock_charts"
os.makedirs(SAVE_DIR, exist_ok=True)


def login_to_screener(driver):
    """Login to screener.in using Google OAuth"""
    try:
        print("Logging into screener.in with Google...")
        driver.get("https://www.screener.in/login/")

        # Wait for and click Google login button
        try:
            google_login_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, 'google') or contains(text(), 'Google')]"))
            )
            google_login_button.click()
            print("Clicked Google login button")
        except:
            # Try alternative selectors for Google login
            possible_selectors = [
                "a[href*='google']",
                "button[data-provider='google']",
                ".google-login",
                "[class*='google']"
            ]

            google_button_found = False
            for selector in possible_selectors:
                try:
                    google_button = driver.find_element(By.CSS_SELECTOR, selector)
                    google_button.click()
                    google_button_found = True
                    print(f"Clicked Google login using selector: {selector}")
                    break
                except:
                    continue

            if not google_button_found:
                print("❌ Could not find Google login button")
                return False

        # Wait for Google login page
        print("Waiting for Google login page...")
        WebDriverWait(driver, 15).until(
            lambda d: "accounts.google.com" in d.current_url or "login" not in d.current_url.lower()
        )

        if "accounts.google.com" in driver.current_url:
            print("🔄 Please complete Google login manually in the browser window...")
            print("The script will wait for you to complete the login process.")

            # Wait for user to complete Google login (up to 2 minutes)
            login_completed = False
            for i in range(120):  # Wait up to 2 minutes
                time.sleep(1)
                current_url = driver.current_url

                # Check if we're back on screener.in and not on login page
                if "screener.in" in current_url and "login" not in current_url.lower():
                    login_completed = True
                    break

                # Show progress every 10 seconds
                if i % 10 == 0 and i > 0:
                    print(f"Still waiting for login completion... ({i}s elapsed)")

            if login_completed:
                print("✅ Google login completed successfully!")
                return True
            else:
                print("❌ Login timeout. Please try again.")
                return False
        else:
            # Already logged in or redirected
            print("✅ Login appears to be successful!")
            return True

    except Exception as e:
        print(f"❌ Login error: {e}")
        return False


def create_driver():
    chrome_options = Options()

    # Try to find Chrome in common locations
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
    ]

    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            chrome_options.binary_location = path
            chrome_found = True
            print(f"Found Chrome at: {path}")
            break

    if not chrome_found:
        print("Chrome not found in common locations. Using system default.")
        # Don't set binary_location, let ChromeDriver find it

    # Add Chrome options for better stability
    # chrome_options.add_argument("--headless=new")  # Disabled for Google login
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")  # Speed up loading
    chrome_options.add_argument("--window-size=1920,1080")
    # Rotate user agents to look more natural
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")

    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("Chrome driver created successfully")
        return driver
    except Exception as e:
        print(f"Error creating Chrome driver: {e}")
        raise


def get_stock_links_from_page(driver, page_num):
    """Collect stock links from a specific page"""
    if page_num == 1:
        page_url = SCREEN_BASE_URL
    else:
        # Check if URL already has query parameters
        if '?' in SCREEN_BASE_URL:
            page_url = f"{SCREEN_BASE_URL}&page={page_num}"
        else:
            page_url = f"{SCREEN_BASE_URL}?page={page_num}"

    print(f"  Loading page {page_num}: {page_url}")
    driver.get(page_url)

    try:
        WebDriverWait(driver, 15).until(
            EC.presence_of_all_elements_located((By.CSS_SELECTOR, "a[href*='/company/']"))
        )
    except:
        print(f"  ⚠️ No stock links found on page {page_num}")
        print(f"  Page title: {driver.title}")
        if "register" in driver.title.lower() or "login" in driver.title.lower():
            print("  ⚠️ Page requires authentication. Please check login credentials.")
        return []

    links = []
    elements = driver.find_elements(By.CSS_SELECTOR, "a[href*='/company/']")
    for e in elements:
        href = e.get_attribute("href")
        if "/company/" in href and href not in links:
            links.append(href)

    print(f"  Found {len(links)} stocks on page {page_num}")
    return links


def process_stock(driver, idx, link):
    """Open a stock page, check price, if < 20 take chart screenshot. Returns True if screenshot saved."""
    try:
        print(f"  Loading page...")

        # Add delay before each request to avoid rate limiting
        time.sleep(random.uniform(1, 3))

        driver.get(link)
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "h1"))
        )

        # Check if we got a rate limit page
        page_text = driver.page_source.lower()
        if "too many requests" in page_text or "rate limit" in page_text or "429" in page_text:
            print(f"  ⚠️ Rate limited! Waiting 5 seconds before retrying...")
            time.sleep(5)
            driver.get(link)
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "h1"))
            )

        # Company name - get the second h1 element (index 1)
        company_name = None
        try:
            h1_elements = driver.find_elements(By.TAG_NAME, "h1")
            if len(h1_elements) > 1:
                company_name = h1_elements[1].text.strip()
            elif len(h1_elements) > 0:
                company_name = h1_elements[0].text.strip()
        except:
            pass

        # Check if we got rate limited based on company name
        if company_name and ("too many requests" in company_name.lower() or "rate limit" in company_name.lower()):
            print(f"  ⚠️ Rate limited detected in page content! Waiting 60 seconds...")
            time.sleep(60)
            return False

        if not company_name:
            company_name = f"Stock_{idx}"

        # Create safe filename
        safe_name = "".join(c for c in company_name if c.isalnum() or c in ['-', '_', ' '])
        safe_name = safe_name.replace(' ', '_')[:50]
        if not safe_name:
            safe_name = f"Stock_{idx}"

        # Current price - get the second span.number element (current price, not market cap)
        current_price = None
        try:
            price_elements = driver.find_elements(By.CSS_SELECTOR, "span.number")
            if len(price_elements) > 1:
                # Index 0 = Market Cap, Index 1 = Current Price
                price_text = price_elements[1].text.replace(",", "").replace("₹", "").strip()
                market_cap_text = price_elements[0].text.replace(",", "").replace("₹", "").strip()
                current_price = float(price_text)
                market_cap = float(market_cap_text)
        except:
            pass

        if current_price is None:
            print(f"⚠️ Could not find price for {company_name}, skipping")
            return False

        print(f"  {company_name} → Current Price: ₹{current_price}")
        print(f"  {company_name} → Market Cap: ₹{market_cap}")

        if current_price >= 20 or market_cap <= 500:
            print(f"  ⏭️ Skipping {company_name}")
            return False

        # Go to chart tab
        print(f"  Opening chart...")
        chart_tab_selectors = [
            "//a[contains(text(),'Chart')]",
            "//a[contains(@href,'chart')]",
            "//button[contains(text(),'Chart')]"
        ]

        chart_tab = None
        for selector in chart_tab_selectors:
            try:
                chart_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                break
            except:
                continue

        if chart_tab is None:
            print(f"  ⚠️ Chart tab not found for {company_name}")
            return False

        driver.execute_script("arguments[0].click();", chart_tab)

        # Click "Max" button
        time.sleep(2)
        max_btn_selectors = [
            "//button[contains(text(),'Max')]",
            "//button[contains(text(),'MAX')]",
            "//a[contains(text(),'Max')]"
        ]

        max_btn = None
        for selector in max_btn_selectors:
            try:
                max_btn = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                break
            except:
                continue

        if max_btn:
            driver.execute_script("arguments[0].click();", max_btn)
            print(f"  Set chart to Max timeframe")
        else:
            print(f"  ⚠️ Max button not found, using default timeframe")

        # Wait for chart to load
        time.sleep(3)
        chart_selectors = [
            "div#chart-container",
            ".chart-container",
            "[id*='chart']",
            ".highcharts-container"
        ]

        chart = None
        for selector in chart_selectors:
            try:
                chart = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                break
            except:
                continue

        if chart is None:
            print(f"  ⚠️ Chart not found for {company_name}")
            return False

        # Save screenshot
        file_path = os.path.join(SAVE_DIR, f"{idx}_{safe_name}.png")
        chart.screenshot(file_path)
        print(f"  ✅ Saved: {file_path}")

        # Random sleep (anti-bot)
        time.sleep(random.uniform(3, 7))

        return True  # Successfully saved screenshot

    except (TimeoutException, NoSuchElementException) as e:
        print(f"  ⚠️ Element not found for {link}: {str(e)[:100]}...")
        return False
    except WebDriverException as e:
        print(f"  ❌ WebDriver error for {link}: {str(e)[:100]}...")
        return False
    except Exception as e:
        print(f"  ❌ Unexpected error for {link}: {str(e)[:100]}...")
        return False


def main():
    print("Starting stock screener...")
    print(f"Output directory: {SAVE_DIR}")
    print(f"Will process pages {START_PAGE} to {MAX_PAGES}")

    try:
        driver = create_driver()
    except Exception as e:
        print(f"Failed to create driver: {e}")
        print("Please ensure Chrome browser is installed and accessible.")
        return

    # Login to screener if enabled
    if USE_LOGIN:
        if not login_to_screener(driver):
            print("Failed to login. Exiting...")
            driver.quit()
            return
    else:
        print("⚠️ Login disabled. This may not work with filtered URLs.")
        print("Set USE_LOGIN = True to enable Google login.")

    total_processed = 0
    total_saved = 0

    try:
        for page_num in range(START_PAGE, MAX_PAGES + 1):
            print(f"\n{'='*80}")
            print(f"PROCESSING PAGE {page_num}/{MAX_PAGES}")
            print(f"{'='*80}")

            # Get stock links from current page
            stock_links = get_stock_links_from_page(driver, page_num)

            if not stock_links:
                print(f"No stocks found on page {page_num}. Stopping.")
                break

            # Process each stock on this page
            for idx, link in enumerate(stock_links, start=1):
                total_processed += 1
                print(f"\n{'-'*60}")
                print(f"Stock {total_processed} (Page {page_num}, Item {idx}/{len(stock_links)}): {link}")
                print(f"{'-'*60}")

                result = process_stock(driver, total_processed, link)
                if result:  # If screenshot was saved
                    total_saved += 1

                # Restart driver every 15 stocks to prevent crash (less frequent to reduce rate limiting)
                if total_processed % 15 == 0:
                    print("Restarting driver to prevent memory issues...")
                    try:
                        driver.quit()
                    except:
                        pass
                    try:
                        driver = create_driver()
                    except Exception as e:
                        print(f"Failed to restart driver: {e}")
                        return

            print(f"\nCompleted page {page_num}. Processed {len(stock_links)} stocks.")
            print(f"Total processed so far: {total_processed} stocks, {total_saved} screenshots saved.")

            # Save progress to file
            with open("screener_progress.txt", "w") as f:
                f.write(f"Last completed page: {page_num}\n")
                f.write(f"Total processed: {total_processed}\n")
                f.write(f"Total saved: {total_saved}\n")
                f.write(f"Next page to process: {page_num + 1}\n")

            # Longer delay between pages to avoid rate limiting
            print(f"Waiting 5 seconds before next page...")
            time.sleep(5)

        print(f"\n{'='*80}")
        print(f"COMPLETED ALL PAGES!")
        print(f"Total stocks processed: {total_processed}")
        print(f"Total screenshots saved: {total_saved}")
        print(f"Screenshots saved in: {SAVE_DIR}")
        print(f"{'='*80}")

    except KeyboardInterrupt:
        print(f"\nProcess interrupted by user.")
        print(f"Processed {total_processed} stocks, saved {total_saved} screenshots.")
    except Exception as e:
        print(f"Unexpected error in main: {e}")
    finally:
        try:
            driver.quit()
            print("Driver closed successfully.")
        except:
            pass


if __name__ == "__main__":
    main()
